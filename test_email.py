#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from postmarker.core import PostmarkClient
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_email_send():
    """Prueba simple de envío de email con Postmark"""
    try:
        # Token de Postmark
        postmark = PostmarkClient(server_token='************************************')
        
        # Enviar email de prueba
        response = postmark.emails.send(
            From='Sistema Monitoreo <<EMAIL>>',
            To='<EMAIL>',
            Subject='Prueba de envío de email - Test',
            HtmlBody='<h1>Prueba de envío</h1><p>Este es un email de prueba para verificar que Postmark funciona correctamente.</p>',
            TextBody='Prueba de envío\n\nEste es un email de prueba para verificar que Postmark funciona correctamente.'
        )
        
        logger.info("✅ Email enviado correctamente")
        logger.info("Response: {}".format(response))
        return True
        
    except Exception as e:
        logger.error("❌ Error enviando email: {}".format(e))
        return False

if __name__ == "__main__":
    test_email_send() 