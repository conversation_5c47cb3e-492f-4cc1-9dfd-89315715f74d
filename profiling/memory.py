import os
import sys
from collections import deque
from collections.abc import Mapping, Set
from numbers import Number


def human_readable_size(size, decimal_places=2):
    """
    Convert a size in bytes to a human readable format
    :param size: Size in bytes
    :param decimal_places: Number of decimal places to show
    :return: Human readable size
    """
    for unit in ['B', 'KiB', 'MiB', 'GiB', 'TiB']:
        if size < 1024.0:
            break
        size /= 1024.0
    return f"{size:.{decimal_places}f}{unit}"


def get_memory_usage(percentage=False):
    import psutil
    process = psutil.Process(os.getpid())
    return round(process.memory_percent() * 100, 2) if percentage else process.memory_info().rss

def get_size(target):
    """
    Get the size of an object and all its children in bytes
    :param target: Object to get the size of
    :return: Size in bytes
    Reference: https://stackoverflow.com/questions/449560/how-do-i-determine-the-size-of-an-object-in-python
    """
    zero_depth_types = (str, bytes, Number, range, bytearray)
    _seen_ids = set()

    def inner(obj):
        obj_id = id(obj)
        if obj_id in _seen_ids:
            return 0
        _seen_ids.add(obj_id)
        size = sys.getsizeof(obj)
        if isinstance(obj, zero_depth_types):
            pass
        elif isinstance(obj, (tuple, list, Set, deque)):
            size += sum(inner(i) for i in obj)
        elif isinstance(obj, Mapping) or hasattr(obj, 'items'):
            size += sum(inner(k) + inner(v) for k, v in getattr(obj, 'items')())

        if hasattr(obj, '__dict__'):
            size += inner(vars(obj))
        if hasattr(obj, '__slots__'):  # can have __slots__ with __dict__
            size += sum(inner(getattr(obj, s)) for s in obj.__slots__ if hasattr(obj, s))
        return size

    return inner(target)
