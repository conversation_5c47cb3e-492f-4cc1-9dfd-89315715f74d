import calendar
import logging
import random
import sys

from paraty_commons_3.common_data.hotel_zone_utils import get_eu_hotels, get_usa_hotels
from paraty_commons_3.decorators.timerdecorator import timeit
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, get_all_hotel_metadata

sys.path.append("..")

import json
import requests
from requests.auth import HTTPBasicAuth
from datetime import date
import datetime


__author__ = 'fmatheis'

import argparse


'''
This script is an adaptation of the existing tool at HotelManager.

It is used to launch a battery of tests against two versions of hotel-manager to verify what is different between the two

'''

SEARCH_TEMPLATE = "/search/?applicationId={applicationId}&countryCode={countryCode}&startDate={startDate}&endDate={endDate}&numRooms={numRooms}&adultsRoom1={numAdults}&adultsRoom2=2&adultsRoom3=2&childrenRoom1={numKids}&childrenRoom2=0&childrenRoom3=0&babiesRoom1=0&babiesRoom2=0&babiesRoom3=0&ignoreStats=True&promocode="

DATE_FORMAT = '%d/%m/%Y'


def _build_random_country_code():
    percentage = random.randrange(100)

    if percentage < 70:
        return 'es'

    return 'gb'


def add_months(sourcedate, months):
    month = sourcedate.month - 1 + months
    year = sourcedate.year + month / 12
    month = month % 12 + 1
    day = min(sourcedate.day, calendar.monthrange(int(year), month)[1])

    return datetime.date(int(year), month, day)


def _build_random_dates(search_calendar):

    #If calendar we need to do the search for 2 months
    if search_calendar:
        offset = random.randrange(6)
        today = datetime.date.today()
        year = today.year
        if today.month + offset > 12:
            year += 1

        startDate = datetime.date(year, ((today.month + offset)) % 12 + 1, 1)
        endDate = add_months(startDate, 2)

    else:
        offset = random.randrange(255)
        percentage = random.randrange(100)

        length = 1
        if percentage < 20:
            length = 2

        elif percentage < 40:
            length = 4

        elif percentage < 60:
            length = 6

        elif percentage < 80:
            length = 8

        elif percentage < 90:
            length = 12

        startDate = date.today() + datetime.timedelta(days=offset)

        endDate = startDate + datetime.timedelta(days=length)

    return startDate.strftime(DATE_FORMAT), endDate.strftime(DATE_FORMAT)


def _build_random_num_rooms():
    percentage = random.randrange(100)

    if percentage < 92:
        return 1

    if percentage < 97:
        return 2

    return 3


def _build_random_num_adults():
    percentage = random.randrange(100)

    if percentage < 85:
        return 2

    if percentage < 91:
        return 1

    if percentage < 97:
        return 3

    return 4


def _build_random_num_kids():

    percentage = random.randrange(100)

    if percentage < 70:
        return 0

    if percentage < 90:
        return 1

    return 2


def _build_random_query(applicationId, calendar=None):

    startDate, endDate = _build_random_dates(calendar)

    logging.info("Start date: %s, end date: %s" % (startDate, endDate))

    result= SEARCH_TEMPLATE.format(applicationId=applicationId,
                                  countryCode=_build_random_country_code(),
                                  startDate=startDate,
                                  endDate=endDate,
                                  numRooms=_build_random_num_rooms(),
                                  numAdults=_build_random_num_adults(),
                                  numKids=_build_random_num_kids(),
                                  )
    if calendar:
        result += "&acceptIncompleteRates=true"

    return result


def _build_url_from_version(version):

    if version.startswith("http"):
        return version

    return 'https://%s-dot-admin-hotel.appspot.com' % version

def _get_hotels(my_hotel_filter=None):
    result = [x['applicationId'] for x in get_all_valid_hotels()]

    if my_hotel_filter:
        return filter(my_hotel_filter, result)


def has_more_than_zeros(d):
    for k, v in d.iteritems():
        if isinstance(v, dict):
            if has_more_than_zeros(v):
                return True
        else:
            if k == 'value' and v > 0.0:
                return True
    return False


def compare_dicts(dict1, dict2, path=""):
    if isinstance(dict1, dict) and isinstance(dict2, dict):
        if len(dict1) != len(dict2):
            print(f"Different keys at path '{path}': {dict1.keys()} != {dict2.keys()}")
            return False

        result = True
        for key in dict1:
            if key not in dict2:
                print(f"Key '{key}' missing at path '{path}'")
                result = False
            else:
                if not compare_dicts(dict1[key], dict2[key], f"{path}/{key}"):
                    result = False

        return result

    elif isinstance(dict1, list) and isinstance(dict2, list):
        if len(dict1) != len(dict2):
            print(f"Different list lengths at path '{path}': {len(dict1)} != {len(dict2)}")
            return False

        result = True
        for i, (item1, item2) in enumerate(zip(dict1, dict2)):
            if not compare_dicts(item1, item2, f"{path}[{i}]"):
                result = False

        return result



@timeit
def _test_fuerte(num_searches):

    CALENDAR = True

    print("Calendar: %s" % CALENDAR)

    URL_1 = 'https://fuerte-adapter.appspot.com'
    URL_2 = 'https://fuerte-adapter.appspot.com'

    valid_hotels = ['olee-calaceite', 'amare-marbella', 'amare-ibiza', 'fuerte-marbella', 'fuerte-grazalema', 'fuerte-rompido', 'fuerte-conil-costaluz']

    print(f'Found {len(valid_hotels)} hotels')

    return _do_generic_test(num_searches, URL_1, URL_2, valid_hotels, calendar_searches=CALENDAR)


@timeit
def _test_USA(num_searches):

    CALENDAR = False
    URL_1 = 'https://admin-hotel.appspot.com'
    URL_2 = 'https://2023-10-18-dot-admin-hotel.appspot.com'

    valid_hotels = get_usa_hotels()

    # valid_hotels = [x for x in valid_hotels if 'parkroyal' in x]

    print(f'Found {len(valid_hotels)} hotels')

    return _do_generic_test(num_searches, URL_1, URL_2, valid_hotels, calendar_searches=CALENDAR)

def _test_USA_vs_EU(num_searches, hotel_codes):
    CALENDAR = False
    URL_1 = 'https://admin-hotel.appspot.com'
    URL_2 = 'https://admin-hotel3.appspot.com'

    valid_hotels = [{'applicationId': x} for x in hotel_codes]

    print(f'Verifying admin-hotel3 vs admin-hotel for hotels: {hotel_codes}')

    return _do_generic_test(num_searches, URL_1, URL_2, valid_hotels, calendar_searches=CALENDAR)


@timeit
def _test_europe(num_searches):

    CALENDAR = True
    URL_1 = 'https://background-dot-admin-hotel3.appspot.com'
    URL_2 = 'https://background-dot-admin-hotel3.appspot.com'

    valid_hotels = get_eu_hotels()

    # valid_hotels = [x for x in valid_hotels if 'casual' in x]

    print(f'Found {len(valid_hotels)} hotels in Europe')

    return _do_generic_test(num_searches, URL_1, URL_2, valid_hotels, calendar_searches=CALENDAR)


def _do_generic_test(num_searches, manager_url_1, manager_url_2, valid_hotels, calendar_searches):

    failures = 0

    for i in range(num_searches):

        hotel_code = random.choice(valid_hotels)
        search = _build_random_query(hotel_code, calendar_searches)

        sys.stdout.flush()

        try:
            request1 = requests.get(manager_url_1 + search, auth=HTTPBasicAuth('paco', 'paco'), timeout=30)
            request2 = requests.get(manager_url_2 + search, auth=HTTPBasicAuth('paco', 'paco'), timeout=30)

            result1, time1 = request1.content, request1.elapsed.total_seconds()
            result2, time2 = request2.content, request2.elapsed.total_seconds()

            print('%s - %s, %s : %s' % (i, hotel_code, time1, time2))

            if result1 != result2:

                if compare_dicts(json.loads(result1), json.loads(result2)):
                    print()
                    print('KO: %s' % search)
                    failures += 1
                    print()

            # print '\n-----------------------------------------\n'

        except Exception as e:
            failures += 1
            print("KO: %s" % e)


    print()
    print()
    print('SUMMARY:')
    print('Tested: %s' % num_searches)
    print('Failures: %s' % failures)
    print()

    return num_searches, failures


if __name__ == "__main__":
    _test_USA_vs_EU(1000, ['angela', 'playamaro', 'estudios-salinas', 'sno-aragon'])

    # _test_europe(1000)
    # _test_USA(1000)
    # _test_fuerte(1000)
