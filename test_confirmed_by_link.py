#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
from datetime import datetime

def test_confirmed_by_link():
    """
    Prueba la funcionalidad de contar reservas confirmadas por link
    """
    print("🔧 PRUEBA: CONFIRMADAS POR LINK")
    print("=" * 50)
    print()
    
    # Simular datos de reservas con diferentes tipos de pago
    reservations_data = [
        {
            'identifier': 'RES001',
            'extraInfo': json.dumps({
                'payed': 150.00,
                'payed_by_tpv_link': [
                    {'amount': 150.00, 'date': '2024-01-15'}
                ]
            }),
            'price': 150.00,
            'priceSupplements': 0
        },
        {
            'identifier': 'RES002',
            'extraInfo': json.dumps({
                'payed': 200.00,
                'payed_by_cobrador': 200.00
            }),
            'price': 200.00,
            'priceSupplements': 0
        },
        {
            'identifier': 'RES003',
            'extraInfo': json.dumps({
                'payed': 180.00,
                'payed_by_tpv_link': [
                    {'amount': 100.00, 'date': '2024-01-15'},
                    {'amount': 80.00, 'date': '2024-01-16'}
                ]
            }),
            'price': 180.00,
            'priceSupplements': 0
        },
        {
            'identifier': 'RES004',
            'extraInfo': json.dumps({
                'payed': 0.00
            }),
            'price': 120.00,
            'priceSupplements': 0
        }
    ]
    
    # Contadores
    total_reservations = len(reservations_data)
    fully_paid = 0
    confirmed_by_link = 0
    
    print("📋 ANÁLISIS DE RESERVAS:")
    print()
    
    for reservation in reservations_data:
        identifier = reservation['identifier']
        extra_info = json.loads(reservation['extraInfo'])
        total_price = reservation['price'] + reservation['priceSupplements']
        total_paid = float(extra_info.get('payed', 0) or 0)
        
        # Verificar si está completamente pagada
        is_fully_paid = total_paid >= total_price
        has_tpv_link = bool(extra_info.get('payed_by_tpv_link'))
        
        print("📋 {}:".format(identifier))
        print("   - Total precio: €{:.2f}".format(total_price))
        print("   - Total pagado: €{:.2f}".format(total_paid))
        print("   - Completamente pagada: {}".format("✅" if is_fully_paid else "❌"))
        print("   - Pagada por link: {}".format("✅" if has_tpv_link else "❌"))
        print()
        
        if is_fully_paid:
            fully_paid += 1
            if has_tpv_link:
                confirmed_by_link += 1
    
    print("📊 RESUMEN:")
    print("   - Total reservas: {}".format(total_reservations))
    print("   - Completamente pagadas: {}".format(fully_paid))
    print("   - Confirmadas por link: {}".format(confirmed_by_link))
    print()
    print("✅ Prueba completada. La funcionalidad 'Confirmadas por link' cuenta las reservas")
    print("   que están completamente pagadas Y tienen 'payed_by_tpv_link' en extraInfo.")

if __name__ == "__main__":
    test_confirmed_by_link() 