#!/bin/bash
# Script para navegar a proyectos
# Uso: ./go_to_project.sh <nombre_proyecto>

PROJECTS_DIR="/Users/<USER>/projects"

case "$1" in
    "avalonadapter")
        cd "$PROJECTS_DIR/avalon-adapter"
        echo "Navegando a avalon-adapter"
        ;;
    "baseintegration")
        cd "$PROJECTS_DIR/base-integration"
        echo "Navegando a base-integration"
        ;;
    "baseintegration3")
        cd "$PROJECTS_DIR/base-integration-3"
        echo "Navegando a base-integration-3"
        ;;
    "buildtools2")
        cd "$PROJECTS_DIR/build-tools-2"
        echo "Navegando a build-tools-2"
        ;;
    "dingusadapter")
        cd "$PROJECTS_DIR/dingus-adapter"
        echo "Navegando a dingus-adapter"
        ;;
    "hotelmanager2")
        cd "$PROJECTS_DIR/hotel-manager-2"
        echo "Navegando a hotel-manager-2"
        ;;
    "hotelwebsv3")
        cd "$PROJECTS_DIR/hotel-webs-v3"
        echo "Navegando a hotel-webs-v3"
        ;;
    "innsistadapter")
        cd "$PROJECTS_DIR/innsist-adapter"
        echo "Navegando a innsist-adapter"
        ;;
    "omnibeespulladapter")
        cd "$PROJECTS_DIR/omnibeespull-adapter"
        echo "Navegando a omnibeespull-adapter"
        ;;
    "omnibeespulladapterrefactoring")
        cd "$PROJECTS_DIR/omnibeespull-adapter-refactoring"
        echo "Navegando a omnibeespull-adapter-refactoring"
        ;;
    "paratytechadapter")
        cd "$PROJECTS_DIR/paratytech-adapter"
        echo "Navegando a paratytech-adapter"
        ;;
    "parkroyalseeker")
        cd "$PROJECTS_DIR/parkroyal-seeker"
        echo "Navegando a parkroyal-seeker"
        ;;
    "paymentseeker")
        cd "$PROJECTS_DIR/payment-seeker"
        echo "Navegando a payment-seeker"
        ;;
    "prestigeadapter")
        cd "$PROJECTS_DIR/prestige-adapter"
        echo "Navegando a prestige-adapter"
        ;;
    "proxyseeker")
        cd "$PROJECTS_DIR/proxy-seeker"
        echo "Navegando a proxy-seeker"
        ;;
    "siteminderadapter")
        cd "$PROJECTS_DIR/siteminder-adapter"
        echo "Navegando a siteminder-adapter"
        ;;
    "sushicounter")
        cd "$PROJECTS_DIR/sushi-counter"
        echo "Navegando a sushi-counter"
        ;;
    "virtualenvs")
        cd "$PROJECTS_DIR/virtual-envs"
        echo "Navegando a virtual-envs"
        ;;
    "yieldplanetadapter")
        cd "$PROJECTS_DIR/yieldplanet-adapter"
        echo "Navegando a yieldplanet-adapter"
        ;;
    *)
        echo "Proyectos disponibles:"
        echo "  avalonadapter -> avalon-adapter"
        echo "  baseintegration -> base-integration"
        echo "  baseintegration3 -> base-integration-3"
        echo "  buildtools2 -> build-tools-2"
        echo "  dingusadapter -> dingus-adapter"
        echo "  hotelmanager2 -> hotel-manager-2"
        echo "  hotelwebsv3 -> hotel-webs-v3"
        echo "  innsistadapter -> innsist-adapter"
        echo "  omnibeespulladapter -> omnibeespull-adapter"
        echo "  omnibeespulladapterrefactoring -> omnibeespull-adapter-refactoring"
        echo "  paratytechadapter -> paratytech-adapter"
        echo "  parkroyalseeker -> parkroyal-seeker"
        echo "  paymentseeker -> payment-seeker"
        echo "  prestigeadapter -> prestige-adapter"
        echo "  proxyseeker -> proxy-seeker"
        echo "  siteminderadapter -> siteminder-adapter"
        echo "  sushicounter -> sushi-counter"
        echo "  virtualenvs -> virtual-envs"
        echo "  yieldplanetadapter -> yieldplanet-adapter"
        ;;
esac