from paraty_commons_3.profiling.memory import get_size


def retrieve_all_cache(all_results, all_locks, name_regex=None, include_value=False, include_size=False):
    """
    Retrieve all cache entries from a list of cache results and locks
    :param all_results: list of cache results
    :param all_locks: list of cache locks
    :param name_regex: regex to filter cache entries
    :param include_value: include cache values in the result
    :param include_size: include cache sizes in the result
    :return: dictionary with cache entries. Format:
    {
        'total_entries': 1,
        'total_size': 1024, # optional
        'entries': [
            {
                'key': 'key1',
                'value': 'value1', # optional
                'size': 1024 # optional
            }
        ]
    }
    """
    total_entries = 0
    total_size = 0
    found_entries = []

    for i, result in enumerate(all_results):
        for key_cached in result:
            if not name_regex or name_regex in key_cached:
                with all_locks[i]:
                    total_entries += 1
                    item = {
                        'key': key_cached
                    }

                    if include_value:
                        item['value'] = result[key_cached]

                    if include_size:
                        size = get_size(result[key_cached])
                        item['size'] = size
                        total_size += size

                    found_entries.append(item)

    if include_size:
        found_entries = list(sorted(found_entries, key=lambda entry: entry.get('size'), reverse=True))

    result = {
        'total_entries': total_entries,
        'entries': found_entries
    }

    if include_size:
        result['total_size'] = total_size

    return result