import datetime
import json

from paraty_commons_3.date_utils import date_to_string
from paraty_commons_3.logging.my_gae_logging import logging

from flask import request
from paraty_commons_3.common_data.common_data_provider import get_reservations_of_hotel
from paraty_commons_3.datastore.datastore_communicator import save_entity_multi

DATATRANS_IP_1 = '193.16.220.'
DATATRANS_IP_2 = '91.223.186.'
PAYNOPAIN_IP = "52.48.131."
PARATY_OFFICE_IP = '**************'

PCI_COMPANY_IPS = [
    DATATRANS_IP_1,
    DATATRANS_IP_2,
    PAYNOPAIN_IP
]

def _update_reservations_with_pci_timestamp(hotel, reservations, extra_infos):
    """Update Reservation.extraInfo.pciTimestamp the first time a reservation is excluded"""
    now = date_to_string(datetime.datetime.now(), format="%Y-%m-%d %H:%M:%S")
    for reservation, extra_info in zip(reservations, extra_infos):
        if "pciTimestamp" not in extra_info:
            # sure is firstime
            extra_info["pciTimestamp"] = now
        else:
            extra_info["pciTimestamp"] = now
            extra_info["pciTimestamp2"] = now
        reservation["extraInfo"] = json.dumps(extra_info)

    logging.info("[PCI] Ready to update {} reservations for hotel {}".format(len(reservations), hotel['applicationId']))
    try:
        #fmatheis, Note that empty fields are failing in REST server, so we have to remove them (i.e. roomType3)
        clean_reservations = []
        # for reservation in reservations:
        #     clean_reservations.append({k: v for k, v in reservation.items() if v is not None})

        result = save_entity_multi(reservations, hotel['applicationId'])
    except Exception as e:
        logging.error(json.dumps(reservations))
        raise e


    logging.info("[PCI] Updated {} entities.".format(len(reservations)))


def _retrieve_reservations_for_update(hotel, reservations):
    """Retrieve the reservations for update in parallel"""
    result = {}
    for reservation in reservations:
        identifier = _get_reservation_identifier(reservation)
        if not identifier:
            continue

        reservation = get_reservations_of_hotel(hotel, None, None, identifier, include_cancelled_reservations=False, discard_test_reservations=False, include_modified_reservations=False)
        if reservation:
            if 'id' in reservation[0]:
                reservation[0].pop('id')

            result[identifier] = reservation

    return result


def _check_ip(request, ip):
    """Check the ip of a request"""
    real_ip = request.headers.get('X-Appengine-User-Ip')
    if not real_ip:
        real_ip = request.environ.get('HTTP_X_REAL_IP', request.remote_addr)

    logging.info("Real IP: %s", real_ip)
    return real_ip.startswith(ip)


def _request_comes_from_pci_compay(request):
    """Check if a request comes from a PCI company"""
    return any([_check_ip(request, ip) for ip in PCI_COMPANY_IPS])


def _get_reservation_identifier(reservation):
    """Get the reservation identifier"""
    if 'reservationDayPrice' in reservation:
        identifier_holder = reservation.get("reservationDayPrice", {})  # parity
    else:
        identifier_holder = reservation  # dingus
    return identifier_holder.get("identifier", None)


def filter_for_pci_company(hotel, reservations, send_twice=False, only_mark_reservation=False):
    """We try to detect if a request comes from som PCI company"""
    logging.info("IP from: %s", request.remote_addr)
    logging.info("IPs to check: %s", PCI_COMPANY_IPS)
    if not _request_comes_from_pci_compay(request):
        logging.info("[PCI] Request don't come from a PCI company. Showing all.")
        return reservations

    to_exclude = []
    to_update = {
        'entity': [],
        'extra_info': []
    }

    # Reservations can be duplicated
    reservations = [i for n, i in enumerate(reservations) if i not in reservations[n + 1:]]
    reservations_4_update = _retrieve_reservations_for_update(hotel, reservations)

    for reservation in reservations:
        identifier = _get_reservation_identifier(reservation)
        if not identifier:
            continue

        reservation_4_update = reservations_4_update.get(identifier)

        if not reservation_4_update:
            logging.error("[PCI] No reservation found for reservation {}.".format(reservation))
            continue

        reservation_4_update = reservation_4_update[0]
        extra_info = json.loads(reservation_4_update.get("extraInfo", "{}"))
        if 'pciTimestamp' not in extra_info:
            to_update['entity'].append(reservation_4_update)
            to_update['extra_info'].append(extra_info)
            continue

        pci_timestamp = extra_info.get('pciTimestamp')
        second_pci_timestamp = extra_info.get('pciTimestamp2')
        second_pci_timestamp_if_necesary = send_twice and (not second_pci_timestamp)


        if not pci_timestamp or second_pci_timestamp_if_necesary:
            to_update['entity'].append(reservation_4_update)
            to_update['extra_info'].append(extra_info)
            logging.info("Showing reservation: %s. Because of First time -> %s Because of Second Time -> %s",identifier, pci_timestamp, second_pci_timestamp_if_necesary)
            continue

        modification_timestamp = reservation_4_update.get('modificationTimestamp', "")
        cancellation_timestamp = reservation_4_update.get('cancellationTimestamp', "")
        lastpayment_timestamp = extra_info.get("lastPaymentTimestamp")

        if not second_pci_timestamp:
            modify_control = (modification_timestamp and modification_timestamp > pci_timestamp)
            cancellation_control = (cancellation_timestamp and cancellation_timestamp > pci_timestamp)
            payments_control = (lastpayment_timestamp and lastpayment_timestamp > pci_timestamp)

        else:

            modify_control = (modification_timestamp and modification_timestamp > second_pci_timestamp)
            cancellation_control = (cancellation_timestamp and cancellation_timestamp > second_pci_timestamp)
            payments_control = (lastpayment_timestamp and lastpayment_timestamp > second_pci_timestamp)

        modified = modify_control or cancellation_control or payments_control

        if modified:
            to_update['entity'].append(reservation_4_update)
            to_update['extra_info'].append(extra_info)
        else:
            to_exclude.append(identifier)

    if to_update['entity']:
        _update_reservations_with_pci_timestamp(hotel, to_update['entity'], to_update['extra_info'])
    else:
        logging.info("[PCI] No entities to udpate.")


    logging.info('[PCI] Already sent {}/{} reservations.'.format(len(to_exclude), len(reservations)))
    if only_mark_reservation:

        for reservation in reservations:
            if _get_reservation_identifier(reservation) in to_exclude:
                reservation["not_send_pci_token"] = True

        logging.info("[PCI] reservations with TOKEN filtered but sent: %s", to_exclude)
        return reservations

    else:

        logging.info("[PCI] reservations excluded: %s", to_exclude)
        logging.info("[PCI] reservations updated: %s", [_get_reservation_identifier(x) for x in reservations if _get_reservation_identifier(x) not in to_exclude])
        return [reservation for reservation in reservations
                if _get_reservation_identifier(reservation) not in to_exclude]
