#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para extraer SIDs unicos del archivo de logs CSV
"""

import csv
import re
from urllib.parse import urlparse, parse_qs
from collections import Counter

def extract_sids_from_csv(csv_file_path):
    """
    Extrae todos los SIDs unicos del campo protoPayload.resource del archivo CSV
    
    Args:
        csv_file_path (str): Ruta al archivo CSV
        
    Returns:
        tuple: (set de SIDs unicos, contador de SIDs, total de registros procesados)
    """
    unique_sids = set()
    sid_counter = Counter()
    total_records = 0
    
    # Índice de la columna protoPayload.resource (columna 24 basada en el header)
    resource_column_index = 23  # 0-based index
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        csv_reader = csv.reader(file)
        
        # Leer el header
        header = next(csv_reader)
        print(f"Header columns: {len(header)}")
        
        # Buscar el índice correcto de protoPayload.resource
        try:
            resource_column_index = header.index('protoPayload.resource')
            print(f"Found protoPayload.resource at index: {resource_column_index}")
        except ValueError:
            print("Column 'protoPayload.resource' not found in header. Using default index 23.")
        
        # Procesar cada fila
        for row_num, row in enumerate(csv_reader, start=2):
            total_records += 1
            
            if len(row) > resource_column_index:
                resource_url = row[resource_column_index]
                
                # Extraer SID usando regex
                sid_match = re.search(r'[?&]sid=([a-f0-9-]+)', resource_url)
                if sid_match:
                    sid = sid_match.group(1)
                    unique_sids.add(sid)
                    sid_counter[sid] += 1
            
            # Mostrar progreso cada 1000 registros
            if total_records % 1000 == 0:
                print(f"Procesados {total_records} registros...")
    
    return unique_sids, sid_counter, total_records

def main():
    # Ruta al archivo CSV
    csv_file_path = "/Users/<USER>/projects/build-tools-2/src/paraty/development/downloaded-logs-20250807-130405.csv"
    
    print("Iniciando extraccion de SIDs unicos...")
    print(f"Archivo: {csv_file_path}")
    print("-" * 50)
    
    try:
        unique_sids, sid_counter, total_records = extract_sids_from_csv(csv_file_path)
        
        # Mostrar resultados
        print(f"\n=== RESULTADOS ===")
        print(f"Total de registros procesados: {total_records}")
        print(f"Total de SIDs unicos encontrados: {len(unique_sids)}")
        print(f"Total de apariciones de SIDs: {sum(sid_counter.values())}")
        
        # Mostrar los SIDs mas frecuentes
        print(f"\n=== TOP 10 SIDs MAS FRECUENTES ===")
        for sid, count in sid_counter.most_common(10):
            print(f"{sid}: {count} apariciones")
        
        # Guardar todos los SIDs unicos en un archivo
        output_file = "/Users/<USER>/projects/build-tools-2/unique_sids_output.txt"
        with open(output_file, 'w') as f:
            f.write(f"Total de SIDs unicos: {len(unique_sids)}\n")
            f.write(f"Total de registros procesados: {total_records}\n")
            f.write(f"Total de apariciones de SIDs: {sum(sid_counter.values())}\n\n")
            
            f.write("=== TODOS LOS SIDs UNICOS ===\n")
            for sid in sorted(unique_sids):
                f.write(f"{sid}\n")
            
            f.write(f"\n=== FRECUENCIA DE SIDs ===\n")
            for sid, count in sid_counter.most_common():
                f.write(f"{sid}: {count} apariciones\n")
        
        print(f"\nResultados guardados en: {output_file}")
        
        # Mostrar algunos SIDs de ejemplo
        print(f"\n=== ALGUNOS SIDs DE EJEMPLO ===")
        for i, sid in enumerate(sorted(unique_sids)[:5]):
            print(f"{i+1}. {sid}")
        
        if len(unique_sids) > 5:
            print("...")
    
    except FileNotFoundError:
        print(f"Error: No se pudo encontrar el archivo {csv_file_path}")
    except Exception as e:
        print(f"Error procesando el archivo: {e}")

if __name__ == "__main__":
    main()
