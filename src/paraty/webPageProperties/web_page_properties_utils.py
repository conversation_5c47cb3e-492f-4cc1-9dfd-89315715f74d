import json
import threading
import time

import requests
from cachetools import TTLCache

from paraty.webPageProperties.cached_wpp import get_cached_wpp_for_hotel, set_cached_wpp_for_hotel, \
	cache_value_has_change, delete_cached_wpp_for_hotel
from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_value
from paraty_commons_3.concurrency.concurrency_utils import execute_in_parallel
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import save_entity
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels
from paraty_commons_3.language_utils import LANGUAGE_TITLES
from paraty_commons_3.logging.my_gae_logging import logging
from paraty_commons_3.redis.redis_communicator import build_redis_client

REDIS_HOST = "************"
REDIS_PORT = 6666
REDIS_PASSWORD = "nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ"

DATASTORE_SAFETY_MILLIS = 60000  # Extra margin we give to the datastore to be sure we are not missing any update
LOCK_BY_KEY = TTLCache(maxsize=500, ttl=60)

def validate_web_page_property(hotel_code):

	valid_languages = get_hotel_advance_config_value(hotel_code, 'Hotel Manager Languages')
	if not valid_languages:
		return 'OK'

	for language in valid_languages.split("-"):
		server_wpp = requests.get(f"https://europe-west1-build-tools-2.cloudfunctions.net/get_web_page_properties_for_language?hotel_code={hotel_code}&language={language}").content.decode("utf-8")
		server_wpp = json.loads(server_wpp)['result']
		datastore_wpp = datastore_communicator.get_using_entity_and_params('WebPageProperty', [('languageKey', '=', language)], keys_only=True, hotel_code=hotel_code)
		if len(datastore_wpp) != len(server_wpp):
			logging.warning(f"WebPageProperties for {hotel_code} ({len(server_wpp)} vs {len(datastore_wpp)}) and {language} are not the same, rebuilding")

			datastore_wpp = datastore_communicator.get_using_entity_and_params('WebPageProperty', [], hotel_code=hotel_code)
			for wpp in datastore_wpp:
				if not 'timestamp' in wpp:
					wpp['timestamp'] = _get_utc_now()
					save_entity(wpp, hotel_code=hotel_code)


def refresh_web_page_properties_for_hotel(hotel_code):
	'''
	If something is wrong it rebuilds it
	'''
	redis_client = build_redis_client(REDIS_HOST, password=REDIS_PASSWORD, port=REDIS_PORT)
	valid_languages = get_hotel_advance_config_value(hotel_code, 'Hotel Manager Languages')

	if not valid_languages:
		return 'OK'

	for language in valid_languages.split("-"):
		redis_key = _get_redis_key(hotel_code, language)
		redis_value = redis_client.get(redis_key)

		if redis_value:
			cached_result = json.loads(redis_value)
			web_page_properties = cached_result.get("webPageProperties")

			datastore_wpp = datastore_communicator.get_using_entity_and_params('WebPageProperty', [('languageKey', '=', language)], keys_only=True, hotel_code=hotel_code)

			if len(datastore_wpp) != len(web_page_properties):
				logging.warning(f"WebPageProperties for {hotel_code} ({len(datastore_wpp)} vs {len(web_page_properties)}) and {language} are not the same, rebuilding")
				_refresh_web_page_properties_for_language(hotel_code, language)


def get_web_page_properties_for_language(request):
	hotel_code = request.args.get("hotel_code")
	language = request.args.get("language")

	logging.info(f"get_web_page_properties_for_language - Referer: {request.referrer or 'Unknown'}")

	if not _check_language(language):
		return {"result": []}

	cache_new_timestamp = _get_utc_now() - DATASTORE_SAFETY_MILLIS  # 60 seconds of tolerance in case there is a delay in the datastore

	redis_key = _get_redis_key(hotel_code, language)

	with LOCK_BY_KEY.setdefault(redis_key, threading.Lock()):
		web_page_properties = {}
		cached_result = {}
		cached_value = get_cached_wpp_for_hotel(hotel_code, language)

		original_timestamp = ""

		if cached_value:
			logging.info(f"Cache hit for {redis_key}")
			cached_result = json.loads(cached_value)
			web_page_properties = cached_result.get("webPageProperties")
			original_timestamp = cached_result.get("timestamp")
			datastore_filter = [('timestamp', '>=', original_timestamp - DATASTORE_SAFETY_MILLIS)]
		else:
			logging.info(f"Cache miss for {redis_key}")
			datastore_filter = [('languageKey', '=', language)]

		all_new_wpp = datastore_communicator.get_using_entity_and_params('WebPageProperty', datastore_filter, hotel_code=hotel_code)
		updated_web_page_properties = [wpp for wpp in all_new_wpp if 'languageKey' in wpp and wpp['languageKey'] == language]

		if updated_web_page_properties:
			for updated_web_page_property in updated_web_page_properties:
				_set_entity_key_values(updated_web_page_property)
				web_page_properties[str(updated_web_page_property.key.id)] = updated_web_page_property

			cached_result["webPageProperties"] = web_page_properties
			cached_result["timestamp"] = cache_new_timestamp
			serialized_cached_result = json.dumps(cached_result)
			if cache_value_has_change(hotel_code, language, original_timestamp):
				logging.info("Initial cache value has changed")
				return get_web_page_properties_for_language(request)

			set_cached_wpp_for_hotel(hotel_code, language, serialized_cached_result)

		else:
			serialized_cached_result = json.dumps(cached_result)

		json_result = json.loads(serialized_cached_result)
		web_page_properties = list(json_result["webPageProperties"].values()) if "webPageProperties" in json_result else []

		wpp_length = len(web_page_properties)
		logging.info(redis_key + " - " + str(wpp_length))
		if wpp_length < 150:
			logging.warning(f"Possible error in web page properties, less than 150 properties ({wpp_length}) {hotel_code}")

		target_status_code = 200 if web_page_properties else 404

		return {"result": web_page_properties}, target_status_code


def _cache_value_has_changed(redis_client, redis_key, original_timestamp):
	redis_value = redis_client.get(redis_key)
	if redis_value:
		cached_result = json.loads(redis_value)
		return original_timestamp != cached_result.get("timestamp")

	return False


def refresh_web_page_properties_for_language(request):
	hotel_code = request.args.get("hotel_code")
	language = request.args.get("language")

	if not _check_language(language):
		return {"result": "LANGUAGE NOT RECOGNIZED"}

	return _refresh_web_page_properties_for_language(hotel_code, language)


def _refresh_web_page_properties_for_language(hotel_code, language):

	web_page_properties = list(datastore_communicator.get_using_entity_and_params('WebPageProperty', [('languageKey', '=', language)], hotel_code=hotel_code))
	web_page_properties_dictionary = {}
	for web_page_property in web_page_properties:
		_set_entity_key_values(web_page_property)
		web_page_properties_dictionary[str(web_page_property.key.id)] = web_page_property

	cached_entity = json.dumps({
		"webPageProperties": web_page_properties_dictionary,
		"timestamp": _get_utc_now()
	})

	redis_client = build_redis_client(REDIS_HOST, password=REDIS_PASSWORD, port=REDIS_PORT)
	redis_key = _get_redis_key(hotel_code, language)
	redis_client.set(redis_key, cached_entity)
	return {"result": "OK"}


def clean_web_page_properties_for_hotel(request):
	hotel_code = request.args.get("hotel_code")
	return do_clean_web_page_properties_for_hotel(hotel_code)


def do_clean_web_page_properties_for_hotel(hotel_code):
	redis_client = build_redis_client(REDIS_HOST, password=REDIS_PASSWORD, port=REDIS_PORT)
	redis_prefix = _get_redis_key_prefix(hotel_code)
	for redis_key in redis_client.keys(f"{redis_prefix}*"):
		redis_client.delete(redis_key)

	return {"result": "OK"}


def delete_web_page_property_for_hotel(request):
	hotel_code = request.args.get("hotel_code")
	web_page_property_ids = request.args.get("ids", "").split(",")
	logging.info(f"delete_web_page_property_for_hotel hotel_code:{hotel_code} ids: {request.args.get('ids', '')}")
	if web_page_property_ids:
		delete_cached_wpp_for_hotel(hotel_code, web_page_property_ids)
		return {"result": "OK"}
	else:
		return {"result": "Error: missing keys"}


def _get_utc_now():
	return int(time.time() * 1000)


def _get_redis_key(hotel_code, language):
	return f"wpp_{hotel_code}_{language}"


def _get_redis_key_prefix(hotel_code):
	return f"wpp_{hotel_code}"


def _set_entity_key_values(web_page_property):
	web_page_property["id"] = web_page_property.key.id
	web_page_property["kind"] = web_page_property.key.kind
	web_page_property["project"] = web_page_property.key.project
	web_page_property["namespace"] = web_page_property.key.namespace
	if "propertyGroup" in web_page_property:
		del web_page_property["propertyGroup"]


def _check_language(language):
	return language in LANGUAGE_TITLES or language == "TEST_LANGUAGE"


if __name__ == '__main__':

	# validate_web_page_property("test-hotel")
	# refresh_web_page_properties_for_hotel("habitus-corpo")

	all_hotels = get_all_valid_hotels()
	params = []
	for hotel in all_hotels:
		params.append((hotel['applicationId'],))
		# refresh_web_page_properties_for_hotel(hotel['applicationId'])

	execute_in_parallel(refresh_web_page_properties_for_hotel, params, 10)
	# execute_in_parallel(validate_web_page_property, params, 10)