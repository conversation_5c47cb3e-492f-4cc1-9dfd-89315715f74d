import json
import sys

from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels

sys.path.append('..')
import datetime
import requests

from paraty_commons_3.common_data.common_data_provider import get_integration_configuration_of_hotel, get_reservations_of_hotel




__author__ = 'nmarin'

class SpainTimeZone(datetime.tzinfo):
    def utcoffset(self, dt):
        return datetime.timedelta(hours=1) + self.dst(dt)

    def tzname(self, dt):
        return "Spain"

    def dst(self, dt):
		# FIXME: This only works for Daylight saving time. If daylight saving time is not enabled, it must return 0.
        return datetime.timedelta(hours=1)


def get_cancelled_reservations_from_adapter(adapter_name,  timestamp, to_datetime):

	all_hotels = get_all_valid_hotels()

	spain_now = datetime.datetime.now(SpainTimeZone())
	modification_timestamp = spain_now.strftime("%Y-%m-%d %H:%M:%S")

	print("lets go with: %s hotelesw" % len(all_hotels))

	for hotel in all_hotels:

		if not hotel.get("applicationId", "") == "oasishoteles-tulum":
			continue

		url_push = ""


		try:
			integration_configuration = get_integration_configuration_of_hotel(hotel, adapter_name)
		except Exception as e:
			integration_configuration = False
			#print("ERROR Not permission: %s", hotel)

		if integration_configuration and integration_configuration[0].get("downloadBooking"):

			print("%s adapter found in %s", (adapter_name, hotel["name"]))

			reservations = get_reservations_of_hotel(hotel, timestamp, to_datetime, include_end_date=False, include_cancelled_reservations=True)


			for current_config in integration_configuration:
				if current_config.get('configurations'):
					for x in current_config.get('configurations'):
						if 'url @@ ' in x:
							url_push = x.split(" @@ ")[1]
							break


			#print "url found: %s nun RES: %s" % (url_push, len(reservations))
			if url_push and len(reservations):
				print("")
				print("")
				print("%s Reservations -> %s", hotel["name"], len(reservations))



				if len(reservations):

					for reservation in reservations:

						extra_info = json.loads(reservation.get("extraInfo", "{}"))
						if not extra_info.get("external_identifier"):
							#print("reservstion NOT already sent " + extra_info.get("external_identifier"))
							continue


							print("reservation not cancelled " + extra_info.get("external_identifier"))
							continue

						print(reservation.get("identifier"))
						url_push_reservation = url_push + "&identifier=" +  reservation["identifier"]
						print(url_push_reservation)


						if post_it_really:

							if modify_reservation:
								reservation["modificationTimestamp"] = modification_timestamp
								url = hotel.get("url")
								#rest_client.update(url + "/rest", 'Reservation', reservation)


							#print "pushing: %s" % url_push_reservation
							#print "pushing: %s" % reservation["identifier"]
							#print



							response = requests.post(url_push_reservation)
							if response.status_code == 200:
								print(response.content)
							else:
								print('ERROR reservation could not be pushed')
								print("")




def get_valid_reservations_from_adapter(adapter_name,  timestamp, to_datetime):

	all_hotels = get_all_valid_hotels()

	spain_now = datetime.datetime.now(SpainTimeZone())
	modification_timestamp = spain_now.strftime("%Y-%m-%d %H:%M:%S")

	print("lets go with: %s hotelesw" % len(all_hotels))

	for hotel in all_hotels:

		'''if not hotel.get("applicationId", "") == "oasishoteles-tulum":
			continue'''

		url_push = ""

		if "oasis ho" not in hotel["name"].lower():
			continue

		try:
			integration_configuration = get_integration_configuration_of_hotel(hotel, adapter_name)
		except Exception as e:
			integration_configuration = False
			#print("ERROR Not permission: %s", hotel)

		if integration_configuration and integration_configuration[0].get("downloadBooking"):

			print("%s adapter found in %s", (adapter_name, hotel["name"]))

			reservations = get_reservations_of_hotel(hotel, timestamp, to_datetime, include_end_date=False, include_cancelled_reservations=True)


			for current_config in integration_configuration:
				if current_config.get('configurations'):
					for x in current_config.get('configurations'):
						if 'url @@ ' in x:
							url_push = x.split(" @@ ")[1]
							break


			#print "url found: %s nun RES: %s" % (url_push, len(reservations))
			if url_push and len(reservations):
				print("")
				print("")
				print("%s Reservations -> %s", hotel["name"], len(reservations))



				if len(reservations):

					for reservation in reservations:

						extra_info = json.loads(reservation.get("extraInfo", "{}"))

						if  '@@@TEST@@@' in reservation.get('comments', '').upper():
							#print("reservstion already sent " + extra_info.get("external_identifier"))
							continue

						if not reservation.get('cancelled'):
							#print("Discarting %s because IS CANCELLED", reservation["identifier"])
							continue

						if reservation.get("cancellationTimestamp") < "2023-11-02 0:0:0":
							continue

						'''if reservation.get('startDate') < "2023-08-09":
							#print("Discarting %s because star date is: %s", reservation["identifier"], reservation["startDate"])
							continue'''



						'''if "external_identifier" in extra_info and not extra_info.get("external_identifier"):
							pass
						else:
							continue
						'''


						#if not reservation.get("identifier") == "1D67C1697":
						#	continue

						print(hotel["name"] + ": " + reservation.get("identifier"))
						url_push_reservation = url_push + "&identifier=" +  reservation["identifier"]
						#print(url_push_reservation)


						if post_it_really:

							if modify_reservation:
								reservation["modificationTimestamp"] = modification_timestamp
								url = hotel.get("url")
								#rest_client.update(url + "/rest", 'Reservation', reservation)


							#print "pushing: %s" % url_push_reservation
							#print "pushing: %s" % reservation["identifier"]
							#print



							response = requests.post(url_push_reservation)
							if response.status_code == 200:
								#print(response.content)
								pass
							else:
								print('ERROR reservation could not be pushed '+  reservation["identifier"])


if __name__ == "__main__":

	# if len(sys.argv) < 2:
	# 	print
	# 	print "Usage: python force_push_reservations_full_adapter.py adapter_name"
	# 	print
	# 	sys.exit(1)
	#
	# adapter_name = sys.argv[1]

	post_it_really = True
	modify_reservation = False
	adapter_name = "avalon"

	timestamp = '2023-08-01 00:00:00'
	to_datetime = '2023-11-05 08:35:00'
	print("ALL HAS FINISHED")




	get_valid_reservations_from_adapter(adapter_name, timestamp, to_datetime)


