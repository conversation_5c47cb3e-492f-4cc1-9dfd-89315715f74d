# -*- coding: utf-8 -*-
from paraty.development.cancel_pending_reservations_not_paid import get_automatic_promotion_pending_from_dict

import logging
import json
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache

from datetime import datetime, timedelta
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels, get_account_manager

from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params


def filter_hotels_by_code(hotels_list, filter_text=None):
    """
    Filtra hoteles por código de hotel.
    
    Args:
        hotels_list (list): Lista de hoteles (diccionarios)
        filter_text (str): Texto para filtrar en el código del hotel. Si es None, no filtra.
        
    Returns:
        list: Lista filtrada de hoteles
    """
    if not filter_text:
        return hotels_list
    
    filtered_hotels = []
    for hotel in hotels_list:
        hotel_code = hotel.get("applicationId", "")
        if filter_text.lower() in hotel_code.lower():
            filtered_hotels.append(hotel)
    
    logging.info("Filtro aplicado: '{}' - Hoteles encontrados: {}".format(filter_text, len(filtered_hotels)))
    return filtered_hotels


def get_hotels_with_pep_paylinks(filter_text=None):
    """
    Devuelve una lista de hoteles que tienen configurado PEP_PAYLINKS COBRADOR.
    
    Args:
        filter_text (str): Texto para filtrar en el código del hotel. Si es None, no filtra.
        
    Returns:
        list: Lista de diccionarios con información de los hoteles que tienen pep_paylinks configurado
    """
    # Returns all the Hotels that are enabled and in production and container pep paylinks cobrador
    hotels = get_all_hotels()
    hotels_aux = [x for x in list(hotels.values()) if x.get('enabled') and x.get('inProduction')]
    hotel_result = []
    hotels_without_pep_paylinks = []
    
    # Aplicar filtro si se especifica
    if filter_text:
        hotels_aux = filter_hotels_by_code(hotels_aux, filter_text)
    
    for hotel in hotels_aux:
        if is_valid_hotel_pep_paylink(hotel.get("applicationId")):
            hotel_result.append(hotel)
        else:
            hotels_without_pep_paylinks.append(hotel.get("applicationId"))
        # if len(hotel_result) > 10:
        #     return hotel_result
    logging.info("Hotels without PEP_PAYLINKS COBRADOR: {}".format(len(hotels_without_pep_paylinks)))
    return hotel_result


def get_hotels_summary_info(hotels_list=None, days=3, filter_text=None):
    """
    Obtiene la información necesaria para generar emails de summaries de hoteles.
    Basado en la estructura del archivo pending_should_be_cancelled.py
    
    Args:
        hotels_list (list): Lista de hoteles (diccionarios) o None para obtener todos los hoteles con pep_paylinks
        days (int): Número de días hacia atrás para analizar reservas
        filter_text (str): Texto para filtrar en el código del hotel. Si es None, no filtra.
        
    Returns:
        dict: Diccionario con hotel_code como key y información del hotel como value
    """
    if hotels_list is None:
        hotels_list = get_hotels_with_pep_paylinks(filter_text)
    elif filter_text:
        # Si se proporciona una lista pero también un filtro, aplicar el filtro
        hotels_list = filter_hotels_by_code(hotels_list, filter_text)
    
    # Calcular fechas de análisis
    start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
    end_date = datetime.now().strftime("%Y-%m-%d")
    
    hotels_summary = {}
    
    for hotel in hotels_list:
        hotel_code = hotel.get("applicationId")
        
        if not hotel_code:
            continue
            
        # Inicializar información del hotel
        hotel_info = {
            'hotel_code': hotel_code,
            'hotel_name': hotel.get('name', 'N/A'),
            'total_reservations': 0,
            'pending_reservations': 0,
            'should_be_cancelled': 0,
            'already_cancelled': 0,
            'cancelled_yesterday': 0,
            'fully_paid': 0,
            'confirmed_by_link': 0,
            'reservations': [],
            'hide_cancelled_yesterday': False,
            'emails_to_send_summary': [],
            'pep_configurations': {},
            'account_manager': None,
            'error': None
        }
        
        try:
            # Obtener configuración PEP_PAYLINKS
            pep_paylinks = list(get_using_entity_and_params(
                "IntegrationConfiguration", 
                hotel_code=hotel_code, 
                return_cursor=True,
                search_params=[("name", "=", "PEP_PAYLINKS COBRADOR")]
            ))
            
            if not pep_paylinks:
                hotel_info['error'] = 'No PEP_PAYLINKS configuration'
                hotels_summary[hotel_code] = hotel_info
                continue
                
            pep_config = pep_paylinks[0]
            
            # Obtener configuraciones del hotel
            hotel_info['pep_configurations'] = {
                'expire_hours_link': get_expire_hours_link(pep_config),
                'limit_time_multibanco': get_limit_time_multibanco(pep_config),
                'automatic_cancell_pending': get_automatic_cancel_pending(pep_config)
            }
            
            # Obtener configuración hide_cancelled_yesterday
            hotel_info['hide_cancelled_yesterday'] = get_hide_cancelled_yesterday_from_summary(pep_config)
            
            # Obtener emails de configuración
            hotel_info['emails_to_send_summary'] = get_emails_to_send_summary(pep_config)
            
            # Obtener account manager
            try:
                hotel_info['account_manager'] = get_account_manager(hotel_code)
            except Exception as e:
                logging.warning("Error obteniendo account manager para {}: {}".format(hotel_code, e))
                hotel_info['account_manager'] = None
            
            # Obtener reservas del período
            reservations = list(get_using_entity_and_params(
                "Reservation", 
                hotel_code=hotel_code, 
                return_cursor=True,
                search_params=[
                    ("timestamp", ">", start_date), 
                    ("timestamp", "<", end_date)
                ]
            ))
            
            hotel_info['total_reservations'] = 0
            
            if not reservations:
                hotels_summary[hotel_code] = hotel_info
                continue
            
            # Analizar cada reserva
            for reservation in reservations:
                analysis = analyze_reservation_status(reservation, hotel_info['pep_configurations'], hotel_code)
                if not analysis:
                    continue
                hotel_info['total_reservations'] += 1
                hotel_info['reservations'].append(analysis)
                
                # Contar estadísticas: canceladas ayer y canceladas automáticamente por separado
                if analysis.get('cancelled_yesterday', False):
                    hotel_info['cancelled_yesterday'] += 1
                elif analysis.get('automatic_cancelled', False):
                    hotel_info['already_cancelled'] += 1
                # Nota: Solo contamos canceladas ayer y canceladas automáticamente
            
            # Separar reservas en tres categorías
            filtered_reservations = []  # Para "otras reservas canceladas" (ninguna por ahora)
            automatic_cancelled_reservations = []  # Para apartado de canceladas automáticamente
            cancelled_yesterday_reservations = []  # Para apartado de canceladas ayer
            
            for r in hotel_info['reservations']:
                if r.get('cancelled_yesterday', False):
                    # Canceladas ayer: van a su propio apartado
                    cancelled_yesterday_reservations.append(r)
                elif r.get('automatic_cancelled', False):
                    # Canceladas automáticamente: van a su propio apartado
                    automatic_cancelled_reservations.append(r)
                # Las demás (si las hubiera) irían a filtered_reservations para "otras canceladas"
            
            # Guardar todas las listas por separado para el template
            hotel_info['automatic_cancelled_reservations'] = automatic_cancelled_reservations
            hotel_info['cancelled_yesterday_reservations'] = cancelled_yesterday_reservations
            
            hotel_info['reservations'] = filtered_reservations
                    
        except Exception as e:
            logging.warning("Error procesando hotel {}: {}".format(hotel_code, e))
            hotel_info['error'] = str(e)
        
        hotels_summary[hotel_code] = hotel_info
    
    logging.info("Summaries obtenidos para {} hoteles".format(len(hotels_summary)))
    return hotels_summary


def analyze_reservation_status(reservation, pep_configurations, hotel_code):
    """
    Analiza si una reserva debería estar cancelada.
    Basado en la función del archivo pending_should_be_cancelled.py
    """
    identifier = reservation.get('identifier')
    extra_info = json.loads(reservation.get("extraInfo", "{}"))
    if not extra_info.get('link_payment_sended'):
        return False
    status_reservation = extra_info.get("status_reservation", "confirmed")
    
    analysis = {
        'identifier': identifier,
        'timestamp': reservation.get('timestamp'),
        'startDate': reservation.get('startDate'),
        'endDate': reservation.get('endDate'),
        'email': reservation.get('email', 'N/A'),
        'cancelled': reservation.get('cancelled', False),
        'status': status_reservation,
        'should_be_cancelled': False,
        'total_price': float(reservation.get('price', 0)) + float(reservation.get('priceSupplements', 0)),
        'total_paid': _get_total_payed_amount_from_all_sources(extra_info),
        'reason': '',
        'warnings': [],
        'cancelled_yesterday': False,
        'cancellationTimestamp': reservation.get('cancellationTimestamp')
    }
    
    # Si ya está cancelada, verificar si fue cancelada automáticamente
    if reservation.get('cancelled'):
        # Verificar si tiene "Automatic cancelled" en incidents
        incidents = reservation.get('incidents', '') or ''
        if 'Automatic cancelled' in incidents:
            # Verificar si se canceló ayer
            try:
                cancellation_timestamp = reservation.get('cancellationTimestamp')
                if cancellation_timestamp:
                    cancellation_date = datetime.strptime(cancellation_timestamp, "%Y-%m-%d %H:%M:%S")
                    yesterday = datetime.now() - timedelta(days=1)
                    
                    # Verificar si se canceló ayer (mismo día)
                    if (cancellation_date.date() == yesterday.date()):
                        analysis['cancelled_yesterday'] = True
                        analysis['cancellationTimestamp'] = cancellation_timestamp
                        analysis['reason'] = 'Automatically cancelled yesterday'
                        return analysis
            except Exception as e:
                logging.warning("Error parsing cancellation timestamp for {}: {}".format(identifier, e))
        
            # Es una cancelación automática
            analysis['automatic_cancelled'] = True
            analysis['reason'] = 'Automatically cancelled by system'
            return analysis
        else:
            # Es una cancelación manual, no la incluimos en el reporte
            analysis['ignore'] = True
            return analysis
    
    # Verificar si está completamente pagada
    if fully_paid(reservation, extra_info):
        analysis['reason'] = 'Fully paid - should not be cancelled'
        return analysis
    
    # Solo analizar reservas pendientes
    if status_reservation != "pending":
        analysis['reason'] = 'Status is "{}" - not pending'.format(status_reservation)
        return analysis
    
    # Verificar configuración de cambio de estado automático
    if not pep_configurations.get('automatic_cancell_pending'):
        analysis['reason'] = 'Hotel does not have automatic status change enabled'
        return analysis
    
    # Verificar si no se ha pagado nada
    nothing_paid = get_nothing_paid(extra_info)
    if not nothing_paid:
        analysis['reason'] = 'Some payment detected'
        analysis['warnings'].append('Reservation has partial payment')
        return analysis
    
    # Verificar si es una reserva con SIBS_MULTIBANCO
    has_sibs_multibanco = extra_info.get('SIBS_MULTIBANCO') or (extra_info.get('SIBS') and extra_info.get('SIBS').get('MULTIBANCO'))
    
    # Determinar las horas de expiración según el tipo de pago
    if has_sibs_multibanco:
        # Para SIBS_MULTIBANCO, buscar la configuración limit_time_multibanco
        expire_hours_link = pep_configurations.get('limit_time_multibanco', 24)
        analysis['warnings'].append('SIBS_MULTIBANCO detected - using limit_time_multibanco')
    else:
        # Para otros tipos de pago, usar expire_hours_link normal
        expire_hours_link = pep_configurations.get('expire_hours_link', 24)
    
    expired_timestamp = get_fixed_payment_link_send_date(reservation, expire_hours_link)
    
    if not expired_timestamp:
        analysis['warnings'].append('Could not calculate payment link expiry date')
        analysis['reason'] = 'Cannot determine payment link expiry'
        return analysis
    
    # Agregar 12 horas adicionales al tiempo de expiración del payment link
    # antes de considerar que la reserva debería ser cancelada
    expiry_with_buffer = expired_timestamp + timedelta(hours=12)
    
    now = datetime.now()
    expired_link = expiry_with_buffer < now
    
    analysis['payment_link_expires'] = expired_timestamp.strftime("%Y-%m-%d %H:%M:%S")
    analysis['payment_link_expires_with_buffer'] = expiry_with_buffer.strftime("%Y-%m-%d %H:%M:%S")
    analysis['hours_until_expiry'] = (expired_timestamp - now).total_seconds() / 3600
    analysis['hours_until_expiry_with_buffer'] = (expiry_with_buffer - now).total_seconds() / 3600
    
    if expired_link:
        analysis['should_be_cancelled'] = True
        analysis['reason'] = 'Payment link expired and no payment received'
    else:
        hours_left = (expiry_with_buffer - now).total_seconds() / 3600
        analysis['reason'] = 'Payment link expires in {:.1f} hours'.format(hours_left)
        if hours_left < 2:
            analysis['warnings'].append('Payment link expires soon')
    
    return analysis


def _get_total_payed_amount_from_all_sources(extra_info):
    """Obtiene el total pagado desde todas las fuentes"""
    try:
        total_paid = float(extra_info.get("payed", 0) or 0)
        
        if extra_info.get("payed_by_cobrador"):
            total_paid += float(extra_info.get("payed_by_cobrador", 0))
        
        if extra_info.get("payed_by_tpv_link"):
            for payment in extra_info.get("payed_by_tpv_link", []):
                total_paid += float(payment.get("amount", 0))
        
        return total_paid
    except Exception:
        return 0.0


def fully_paid(reservation, extra_info):
    """Verifica si la reserva está completamente pagada"""
    try:
        total_price = float(reservation.get("price", 0)) + float(reservation.get("priceSupplements", 0))
        total_paid = float(extra_info.get("payed", 0) or 0)
        
        if extra_info.get("payed_by_cobrador"):
            total_paid += float(extra_info.get("payed_by_cobrador", 0))
        
        if extra_info.get("payed_by_tpv_link"):
            for payment in extra_info.get("payed_by_tpv_link", []):
                total_paid += float(payment.get("amount", 0))
        
        return total_paid >= total_price
    except Exception:
        return False


def get_nothing_paid(extra_info):
    """Verifica si no se ha pagado nada en la reserva"""
    return not float(extra_info.get("payed", 0) or 0) and not extra_info.get("payed_by_tpv_link") and not extra_info.get('payed_by_cobrador')


def get_fixed_payment_link_send_date(reservation, expire_hours_link):
    """Calcula la fecha de expiración del link de pago"""
    try:
        extra_info = json.loads(reservation.get("extraInfo", "{}"))
        if extra_info.get("payment_link_send_date"):
            payment_link_send_date = datetime.strptime(extra_info.get("payment_link_send_date"), "%Y-%m-%d %H:%M:%S")
            return payment_link_send_date + timedelta(hours=expire_hours_link)
        
        # Si no hay fecha de envío del link, usar timestamp de la reserva
        initial_ts = reservation.get("modificationTimestamp") or reservation.get("timestamp", "")
        if initial_ts:
            return datetime.strptime(initial_ts, "%Y-%m-%d %H:%M:%S") + timedelta(hours=expire_hours_link)
    except Exception as e:
        logging.warning("Error calculating payment link expiry for {}: {}".format(reservation.get('identifier'), e))
    return None


def get_hotels_emails_by_pep_paylinks_config(hotels_list=None, filter_text=None):
    """
    Obtiene los emails de configuración PEP_PAYLINKS para una lista de hoteles
    y los organiza en un diccionario email: hotel_code.
    
    Args:
        hotels_list (list): Lista de hoteles (diccionarios) o None para obtener todos los hoteles con pep_paylinks
        filter_text (str): Texto para filtrar en el código del hotel. Si es None, no filtra.
        
    Returns:
        dict: Diccionario con emails como keys y listas de códigos de hotel como values
    """
    if hotels_list is None:
        hotels_list = get_hotels_with_pep_paylinks(filter_text)
    elif filter_text:
        # Si se proporciona una lista pero también un filtro, aplicar el filtro
        hotels_list = filter_hotels_by_code(hotels_list, filter_text)
    
    email_to_hotels = {}
    
    for hotel in hotels_list:
        hotel_code = hotel.get("applicationId")
        
        if not hotel_code:
            continue
            
        # Obtener configuración PEP_PAYLINKS
        try:
            pep_paylinks = list(get_using_entity_and_params(
                "IntegrationConfiguration", 
                hotel_code=hotel_code, 
                return_cursor=True,
                search_params=[("name", "=", "PEP_PAYLINKS COBRADOR")]
            ))
            
            if not pep_paylinks:
                logging.warning("Hotel {} no tiene configuración PEP_PAYLINKS COBRADOR".format(hotel_code))
                continue
                
            pep_config = pep_paylinks[0]
            
            # Obtener emails de 'emails to send summary' y exclusiones
            emails_to_send_summary = get_emails_to_send_summary(pep_config)
            excluded_emails = set(get_emails_to_exclude_from_summary(pep_config))
            

            # Combinar todos los emails únicos
            all_emails = set()
            all_emails.update(emails_to_send_summary)
 

            # Agregar hotel a cada email
            for email in all_emails:
                if email.strip():  # Solo agregar si el email no está vacío
                    email_clean = email.strip().lower()
                    if email_clean in excluded_emails:
                        continue
                    if email_clean not in email_to_hotels:
                        email_to_hotels[email_clean] = []
                    email_to_hotels[email_clean].append(hotel_code)
                    
        except Exception as e:
            logging.warning("Error obteniendo configuración PEP_PAYLINKS para hotel {}: {}".format(hotel_code, e))
            continue
    
    logging.info("Emails encontrados: {}".format(len(email_to_hotels)))
    
    return email_to_hotels


def get_hotels_account_managers_by_pep_paylinks(hotels_list=None, filter_text=None):
    """
    Obtiene los account managers de los hoteles con pep_paylinks y los organiza
    en un diccionario email: hotel_code.
    
    Args:
        hotels_list (list): Lista de hoteles (diccionarios) o None para obtener todos los hoteles con pep_paylinks
        filter_text (str): Texto para filtrar en el código del hotel. Si es None, no filtra.
        
    Returns:
        dict: Diccionario con emails de account managers como keys y listas de códigos de hotel como values
    """
    if hotels_list is None:
        hotels_list = get_hotels_with_pep_paylinks(filter_text)
    elif filter_text:
        # Si se proporciona una lista pero también un filtro, aplicar el filtro
        hotels_list = filter_hotels_by_code(hotels_list, filter_text)
    
    account_manager_to_hotels = {}
    
    for hotel in hotels_list:
        hotel_code = hotel.get("applicationId")
        
        if not hotel_code:
            continue
            
        try:
            # Obtener account manager del metadata
            account_manager_email = get_account_manager(hotel_code)
            
            if account_manager_email:
                # Normalizar email a minúsculas
                account_manager_clean = account_manager_email.strip().lower()
                
                # Manejar múltiples emails separados por punto y coma
                if ';' in account_manager_clean:
                    emails = [email.strip() for email in account_manager_clean.split(';') if email.strip()]
                    for email in emails:
                        if email not in account_manager_to_hotels:
                            account_manager_to_hotels[email] = []
                        account_manager_to_hotels[email].append(hotel_code)
                else:
                    if account_manager_clean not in account_manager_to_hotels:
                        account_manager_to_hotels[account_manager_clean] = []
                    account_manager_to_hotels[account_manager_clean].append(hotel_code)
            else:
                logging.warning("Hotel {} no tiene account manager configurado".format(hotel_code))
                
        except Exception as e:
            logging.warning("Error obteniendo account manager para hotel {}: {}".format(hotel_code, e))
            continue
    
    logging.info("Account managers encontrados: {}".format(len(account_manager_to_hotels)))
    
    return account_manager_to_hotels


def get_hotels_all_emails_by_pep_paylinks(hotels_list=None, filter_text=None):
    """
    Obtiene todos los emails (configuración + account managers) de los hoteles con pep_paylinks
    y los organiza en un diccionario email: hotel_code.
    
    Args:
        hotels_list (list): Lista de hoteles (diccionarios) o None para obtener todos los hoteles con pep_paylinks
        filter_text (str): Texto para filtrar en el código del hotel. Si es None, no filtra.
        
    Returns:
        dict: Diccionario con emails como keys y listas de códigos de hotel como values
    """
    if hotels_list is None:
        hotels_list = get_hotels_with_pep_paylinks(filter_text)
    elif filter_text:
        # Si se proporciona una lista pero también un filtro, aplicar el filtro
        hotels_list = filter_hotels_by_code(hotels_list, filter_text)
    
    # Obtener emails de configuración (ya aplica exclusiones por hotel)
    config_emails = get_hotels_emails_by_pep_paylinks_config(hotels_list, filter_text)
    
    # Obtener account managers
    account_manager_emails = get_hotels_account_managers_by_pep_paylinks(hotels_list, filter_text)
    
    # Combinar ambos diccionarios
    all_emails = {}
    
    # Agregar emails de configuración
    for email, hotels in config_emails.items():
        all_emails[email] = hotels.copy()
    
    # Agregar account managers (evitar duplicados de hoteles)
    for email, hotels in account_manager_emails.items():
        if email in all_emails:
            # Si el email ya existe, agregar hoteles únicos
            existing_hotels = set(all_emails[email])
            for hotel in hotels:
                if hotel not in existing_hotels:
                    all_emails[email].append(hotel)
        else:
            all_emails[email] = hotels.copy()
    
    logging.info("Total emails únicos: {}".format(len(all_emails)))
    
    return all_emails


def get_emails_to_send_summary(pep_paylinks):
    """
    Obtiene los emails de 'emails to send summary' de la configuración PEP_PAYLINKS.
    
    Args:
        pep_paylinks (dict): Configuración de PEP_PAYLINKS COBRADOR
        
    Returns:
        list: Lista de emails encontrados
    """
    for configs in pep_paylinks.get("configurations", []):
        if configs.startswith("emails to send summary @@ "):
            emails_str = configs.split(" @@ ", 1)[1]
            try:
                # Separar emails por ; y limpiar espacios
                emails = [email.strip() for email in emails_str.split(';') if email.strip()]
                return emails
            except Exception as e:
                logging.warning("Error parsing 'emails to send summary' string: {}".format(e))
    return []


def get_emails_to_exclude_from_summary(pep_paylinks):
    """
    Obtiene los emails de 'emails to exclude from summary' de la configuración PEP_PAYLINKS.
    Devuelve una lista de emails a excluir (normalizados en minúsculas y sin espacios).
    """
    for configs in pep_paylinks.get("configurations", []):
        if configs.startswith("emails to exclude from summary @@ "):
            emails_str = configs.split(" @@ ", 1)[1]
            try:
                return [email.strip().lower() for email in emails_str.split(';') if email.strip()]
            except Exception as e:
                logging.warning("Error parsing 'emails to exclude from summary' string: {}".format(e))
    return []

def _get_all_integration_configuration(hotel_code):
	all_integrations_configurations = get_using_entity_and_params('IntegrationConfiguration', search_params=[], hotel_code=hotel_code)
	return all_integrations_configurations


def _get_integration_configuration_properties(integration_name, hotel_code):
	all_integration_configs = _get_all_integration_configuration(hotel_code)
	integrationConfig = [x for x in all_integration_configs if x['name'] == integration_name]

	if integrationConfig and len(integrationConfig) > 0:
		result = integrationConfig[0]
		properties = {x.split(" @@ ")[0]: x.split(" @@ ")[1] for x in result.get('configurations', [])}
		return properties

	return {}


# @managers_cache(hotel_code_provider=lambda f, a, k: a[0], entities='IntegrationConfiguration', ttl_seconds=24 * 3600, background_refresh=False)
def is_valid_hotel_pep_paylink(hotel_code):
	pep_paylinks = _get_integration_configuration_properties("PEP_PAYLINKS COBRADOR", hotel_code)
	if pep_paylinks and len(pep_paylinks) > 0:
		automatic_cancel_pending = pep_paylinks.get('automatic_cancell_pending', None)
		automatic_cancel_error_link = pep_paylinks.get('automatic_cancel_error_link', None)
		automatic_promotion, promotion, entry_time_limit = get_automatic_promotion_pending_from_dict(pep_paylinks)
		if automatic_cancel_pending or automatic_promotion or automatic_cancel_error_link:
			logging.info("Hotel {} is valid for PEP_PAYLINKS COBRADOR".format(hotel_code))
			return True
	return False


def _get_pep_paylinks_properties(pep_config):
    """
    Extrae las propiedades de configuración de PEP_PAYLINKS COBRADOR.
    
    Args:
        pep_config (dict): Configuración de PEP_PAYLINKS COBRADOR
        
    Returns:
        dict: Diccionario con las propiedades de configuración
    """
    properties = {}
    
    for config in pep_config.get('configurations', []):
        if " @@ " in config:
            key, value = config.split(" @@ ", 1)
            properties[key] = value
    
    return properties


def get_expire_hours_link(pep_paylinks):
    """Obtiene las horas de expiración del link de pago"""
    for configs in pep_paylinks.get("configurations", []):
        if "expire_hours_link" in configs and (configs.split(" @@ ")[0] == "expire_hours_link"):
            return float(configs.split(" @@ ")[1])
    return 24  # Default


def get_limit_time_multibanco(pep_paylinks):
    """Obtiene el tiempo límite para multibanco desde la configuración SIBS"""
    for configs in pep_paylinks.get("configurations", []):
        if "limit_time_multibanco" in configs and (configs.split(" @@ ")[0] == "limit_time_multibanco"):
            return float(configs.split(" @@ ")[1])
    return 24  # Default


def get_automatic_cancel_pending(pep_paylinks):
    """Verifica si el hotel tiene cambio de estado automático habilitado"""
    for configs in pep_paylinks.get("configurations", []):
        if "automatic_cancell_pending" in configs:
            return configs.split(" @@ ")[1]
    return None


def get_hide_cancelled_yesterday_from_summary(pep_paylinks):
    """Busca la configuración summary y extrae el valor de hide_cancelled_yesterday si existe"""
    for configs in pep_paylinks.get("configurations", []):
        if configs.startswith("summary @@ "):
            summary_str = configs.split(" @@ ", 1)[1]
            try:
                for part in summary_str.split(';'):
                    if part.strip() == 'hide_cancelled_yesterday':
                        return True
            except Exception as e:
                logging.warning("Error parsing summary string: {}".format(e))
    return False





def generate_emails_from_all_emails(all_emails, hotels_summary, days=3):
    """
    Genera emails basándose en all_emails y hotels_summary.
    Itera por cada email y genera el contenido del email con la información de los hoteles.
    Usa la plantilla HTML existente report_pending_should_be_cancelled_account_manager.html
    
    Args:
        all_emails (dict): Diccionario con emails como keys y listas de hotel_codes como values
        hotels_summary (dict): Diccionario con hotel_code como key y información del hotel como value
        days (int): Número de días analizados para incluir en el email
        
    Returns:
        dict: Diccionario con emails como keys y contenido del email como value
    """
    from datetime import datetime
    from jinja2 import Environment, FileSystemLoader
    import os
    
    emails_content = {}
    
    # Configurar Jinja2 para usar la plantilla existente
    template_dir = os.path.join(os.path.dirname(__file__), 'emails', 'output_emails')
    env = Environment(loader=FileSystemLoader(template_dir))
    template = env.get_template('report_pending_should_be_cancelled_account_manager.html')
    
    for email, hotel_codes in all_emails.items():
        # Filtrar hoteles que existen en hotels_summary
        valid_hotels = {code: hotels_summary[code] for code in hotel_codes if code in hotels_summary}
        
        if not valid_hotels:
            continue
            
        # Calcular estadísticas para este email (canceladas automáticamente y canceladas ayer)
        total_hotels = len(valid_hotels)
        total_reservations = sum(hotel['total_reservations'] for hotel in valid_hotels.values())
        total_cancelled_yesterday = sum(hotel['cancelled_yesterday'] for hotel in valid_hotels.values())
        total_already_cancelled = sum(hotel['already_cancelled'] for hotel in valid_hotels.values())
        # No calculamos otras estadísticas porque solo mostramos canceladas
        total_should_cancel = 0
        total_fully_paid = 0
        total_pending = 0
        
        # Crear estructura de datos para la plantilla
        analysis_results = {
            'period': 'Últimos {} días'.format(days),
            'analysis_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Generar contenido HTML usando la plantilla existente
        html_content = template.render(
            account_manager_email=email,
            hotels_data=valid_hotels,
            analysis_results=analysis_results,
            simulation_info=None  # No modo simulación
        )
        
        # Generar contenido texto plano
        text_content = generate_text_email_content(
            email, valid_hotels, days,
            total_hotels, total_reservations, total_should_cancel,
            total_fully_paid, total_pending, total_cancelled_yesterday, total_already_cancelled
        )
        
        # Generar asunto
        subject = generate_email_subject(total_should_cancel, days)
        
        emails_content[email] = {
            'subject': subject,
            'html_content': html_content,
            'text_content': text_content,
            'hotels': valid_hotels,
            'stats': {
                'total_hotels': total_hotels,
                'total_reservations': total_reservations,
                'total_should_cancel': total_should_cancel,
                'total_fully_paid': total_fully_paid,
                'total_pending': total_pending,
                'total_cancelled_yesterday': total_cancelled_yesterday,
                'total_already_cancelled': total_already_cancelled
            }
        }
    
    logging.info("Emails generados: {}".format(len(emails_content)))
    return emails_content


def generate_html_email_content(email, hotels_data, days, total_hotels, total_reservations, 
                              total_should_cancel, total_fully_paid, total_pending, 
                              total_cancelled_yesterday, total_already_cancelled):
    """Genera el contenido HTML del email"""
    
    # Fecha actual
    current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    period = "Últimos {} días".format(days)
    
    # Generar tabla de hoteles
    hotels_table = ""
    for hotel_code, hotel_info in hotels_data.items():
        hotel_name = hotel_info['hotel_name']
        should_cancel = hotel_info['should_be_cancelled']
        total_hotel_reservations = hotel_info['total_reservations']
        fully_paid = hotel_info['fully_paid']
        pending = hotel_info['pending_reservations']
        cancelled_yesterday = hotel_info['cancelled_yesterday']
        already_cancelled = hotel_info['already_cancelled']
        
        # Color de fondo según si tiene problemas
        bg_color = "#ffebee" if should_cancel > 0 else "#f1f8e9"
        
        hotels_table += """
        <tr style="background-color: {};">
            <td style="padding: 8px; border: 1px solid #ddd;"><strong>{}</strong><br><small>{}</small></td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">{}</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: center; color: {};">{}</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">{}</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">{}</td>
            <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">{}</td>
        </tr>
        """.format(bg_color, hotel_name, hotel_code, total_hotel_reservations, 
                   'red' if should_cancel > 0 else 'green', should_cancel, 
                   pending, cancelled_yesterday, already_cancelled)
    
    html_content = """
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background-color: #2196F3; color: white; padding: 20px; border-radius: 5px; }}
            .stats {{ background-color: #f5f5f5; padding: 15px; margin: 20px 0; border-radius: 5px; }}
            .hotels-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
            .hotels-table th {{ background-color: #2196F3; color: white; padding: 10px; text-align: center; }}
            .hotels-table td {{ padding: 8px; border: 1px solid #ddd; }}
            .warning {{ color: #f44336; font-weight: bold; }}
            .success {{ color: #4CAF50; font-weight: bold; }}
            .footer {{ margin-top: 30px; padding: 15px; background-color: #f5f5f5; border-radius: 5px; font-size: 12px; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>📊 Reporte de Reservas PEP_PAYLINKS</h1>
            <p><strong>Período:</strong> {}</p>
            <p><strong>Fecha análisis:</strong> {}</p>
            <p><strong>Destinatario:</strong> {}</p>
        </div>
        
        <div class="stats">
            <h2>📈 Resumen General</h2>
            <p><strong>Hoteles analizados:</strong> {}</p>
            <p><strong>Total reservas:</strong> {}</p>
            <p class="{}">
                <strong>Reservas que requieren cambio de estado:</strong> {}
            </p>
            <p><strong>Reservas pendientes (normales):</strong> {}</p>
            <p><strong>Reservas canceladas automáticamente ayer:</strong> {}</p>
            <p><strong>Reservas ya canceladas:</strong> {}</p>
        </div>
        
        <h2>🏨 Detalle por Hotel</h2>
        <table class="hotels-table">
            <thead>
                <tr>
                    <th>Hotel</th>
                    <th>Total Reservas</th>
                    <th>Deberían Cancelarse</th>
                    <th>Pendientes</th>
                    <th>Canceladas Ayer</th>
                    <th>Ya Canceladas</th>
                </tr>
            </thead>
            <tbody>
                {}
            </tbody>
        </table>
        
        <div class="footer">
            <p><strong>Paraty Tech - Sistema de Monitoreo PEP_PAYLINKS</strong></p>
            <p>Este reporte se genera automáticamente para hoteles con configuración PEP_PAYLINKS COBRADOR.</p>
            <p>Para más información, contacte al equipo de desarrollo.</p>
        </div>
    </body>
    </html>
    """.format(period, current_date, email, total_hotels, total_reservations,
               'warning' if total_should_cancel > 0 else 'success', total_should_cancel,
               total_pending, total_cancelled_yesterday, total_already_cancelled,
               hotels_table)
    
    return html_content


def generate_text_email_content(email, hotels_data, days, total_hotels, total_reservations, 
                              total_should_cancel, total_fully_paid, total_pending, 
                              total_cancelled_yesterday, total_already_cancelled):
    """Genera el contenido de texto plano del email"""
    
    current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    period = "Últimos {} días".format(days)
    
    # Generar lista de hoteles
    hotels_list = ""
    for hotel_code, hotel_info in hotels_data.items():
        hotel_name = hotel_info['hotel_name']
        should_cancel = hotel_info['should_be_cancelled']
        total_hotel_reservations = hotel_info['total_reservations']
        fully_paid = hotel_info['fully_paid']
        pending = hotel_info['pending_reservations']
        cancelled_yesterday = hotel_info['cancelled_yesterday']
        already_cancelled = hotel_info['already_cancelled']
        
        status = "⚠️ PROBLEMA" if should_cancel > 0 else "✅ OK"
        
        hotels_list += """
  • {} ({}) {}
    - Total reservas: {}
    - Deberían cancelarse: {}
    - Pendientes: {}
    - Canceladas ayer: {}
    - Ya canceladas: {}
        """.format(hotel_name, hotel_code, status, total_hotel_reservations,
                   should_cancel, pending, cancelled_yesterday, already_cancelled)
    
    # Generar sección detallada de reservas canceladas ayer
    cancelled_yesterday_detail = ""
    for hotel_code, hotel_info in hotels_data.items():
        cancelled_reservations = hotel_info.get('cancelled_yesterday_reservations', [])
        if cancelled_reservations:
            hotel_name = hotel_info.get('hotel_name', '')
            hotel_display_name = "{} - {}".format(hotel_code.upper(), hotel_name) if hotel_name else hotel_code.upper()
            
            cancelled_yesterday_detail += """

🏨 {}
   Total canceladas ayer: {}
""".format(hotel_display_name, len(cancelled_reservations))
            
            for reservation in cancelled_reservations:
                cancelled_yesterday_detail += """
   📋 {}
      - Estancia: {} - {}
      - Creada: {}
      - Cancelada: {}
      - Total: €{:.2f} | Pagado: €{:.2f}
      - Motivo: {}
""".format(
                    reservation.get('identifier', 'N/A'),
                    reservation.get('startDate', 'N/A'),
                    reservation.get('endDate', 'N/A'),
                    reservation.get('timestamp', 'N/A'),
                    reservation.get('cancellationTimestamp', 'N/A'),
                    float(reservation.get('total_price', 0)),
                    float(reservation.get('total_paid', 0)),
                    reservation.get('reason', 'Cancelación automática')
                )
    
    # Construir contenido completo
    cancelled_yesterday_section = ""
    if cancelled_yesterday_detail.strip():
        cancelled_yesterday_section = """

RESERVAS CANCELADAS AUTOMÁTICAMENTE AYER:
==========================================
Las siguientes reservas fueron canceladas automáticamente por el sistema:
{}
""".format(cancelled_yesterday_detail)

    text_content = """
REPORTE DE RESERVAS PEP_PAYLINKS - TUS HOTELES
===============================================
Destinatario: {}
Período: {}
Fecha análisis: {}

RESUMEN GENERAL:
- Hoteles analizados: {}
- Total reservas: {}
- Reservas que requieren cambio de estado: {}
- Reservas pendientes (normales): {}
- Reservas canceladas automáticamente ayer: {}
- Reservas ya canceladas: {}

DETALLE POR HOTEL:
{}{}

---
Paraty Tech - Sistema de Monitoreo PEP_PAYLINKS
Este reporte se genera automáticamente para hoteles con configuración PEP_PAYLINKS COBRADOR.
Para más información, contacte al equipo de desarrollo.
    """.format(email, period, current_date, total_hotels, total_reservations,
               total_should_cancel, total_pending, total_cancelled_yesterday,
               total_already_cancelled, hotels_list, cancelled_yesterday_section)
    
    return text_content


def generate_email_subject(total_should_cancel, days):
    """Genera el asunto del email basándose en las estadísticas"""
    if total_should_cancel > 0:
        return "⚠️ PEP_PAYLINKS: {} reservas requieren atención - Últimos {} días".format(total_should_cancel, days)
    else:
        return "✅ PEP_PAYLINKS: Reporte de reservas - Últimos {} días".format(days)


def pep_paylinks_summary(filter_text=None, days=3, dev_mode=None):
    """
    Genera y envía el summary de PEP Paylinks para todos los hoteles configurados.
    
    Args:
        filter_text (str): Filtro opcional por código de hotel. Si es None, procesa todos los hoteles con PEP_PAYLINKS
        days (int): Número de días a analizar (por defecto 3)
        dev_mode (bool): Si es True, envía emails en modo desarrollo
        
    Returns:
        tuple: (message, status_code)
    """
    # Obtener todos los hoteles que tengan PEP_PAYLINKS configurado (o filtrados)
    hotels = get_hotels_with_pep_paylinks(filter_text=filter_text)
    if not hotels:
        filter_msg = " with filter '{}'".format(filter_text) if filter_text else ""
        return "No hotels with PEP_PAYLINKS found{}".format(filter_msg), 200

    # Generar summary y emails para todos los hoteles
    hotels_summary = get_hotels_summary_info(hotels_list=hotels, days=days)
    all_emails = get_hotels_all_emails_by_pep_paylinks(hotels_list=hotels)
    emails_content = generate_emails_from_all_emails(all_emails, hotels_summary, days=days)

    # Enviar emails (respeta DEV_MODE interno de la función)
    emails_sent = send_pep_paylinks_emails_with_conditions(emails_content, days=days, dev_mode=dev_mode)

    filter_msg = " (filter: {})".format(filter_text) if filter_text else ""
    return "OK - {} emails sent for {} hotels{} (dev={})".format(emails_sent, len(hotels), filter_msg, dev_mode), 200


def pep_paylinks_summary_test_mode(filter_text=None, days=3):
    """
    Función de TEST que NO ENVÍA emails reales, solo muestra qué se enviaría.
    
    Args:
        filter_text (str): Filtro opcional por código de hotel
        days (int): Número de días a analizar (por defecto 3)
        
    Returns:
        tuple: (message, status_code)
    """
    logging.info("🧪 MODO TEST - NO SE ENVIARÁN EMAILS REALES")
    
    # Obtener todos los hoteles que tengan PEP_PAYLINKS configurado (o filtrados)
    hotels = get_hotels_with_pep_paylinks(filter_text=filter_text)
    if not hotels:
        filter_msg = " with filter '{}'".format(filter_text) if filter_text else ""
        return "No hotels with PEP_PAYLINKS found{}".format(filter_msg), 200

    # Generar summary y emails para todos los hoteles
    hotels_summary = get_hotels_summary_info(hotels_list=hotels, days=days)
    all_emails = get_hotels_all_emails_by_pep_paylinks(hotels_list=hotels)
    emails_content = generate_emails_from_all_emails(all_emails, hotels_summary, days=days)

    # NO ENVIAR - solo mostrar qué se enviaría
    logging.info("📊 RESULTADOS DEL TEST:")
    logging.info("  - Hoteles procesados: {}".format(len(hotels)))
    logging.info("  - Emails que se enviarían: {}".format(len(emails_content)))
    
    for email, email_info in emails_content.items():
        stats = email_info.get('stats', {})
        hotels_count = stats.get('total_hotels', 0)
        logging.info("  - Para {}: {} hoteles".format(email, hotels_count))
    
    filter_msg = " (filter: {})".format(filter_text) if filter_text else ""
    return "TEST MODE - Would send {} emails for {} hotels{}".format(len(emails_content), len(hotels), filter_msg), 200


# Constantes para envío de emails
PARATY_EPAYMENT_SUPERVISORS = "<EMAIL>"
DEV_MODE = False


def send_pep_paylinks_emails(emails_content, days=3):
    """
    Envía los emails generados a los destinatarios correspondientes.
    
    Args:
        emails_content (dict): Diccionario con emails como keys y contenido como value
        days (int): Número de días analizados
        
    Returns:
        int: Número de emails enviados exitosamente
    """
    from postmarker.core import PostmarkClient
    
    emails_sent = 0
    total_emails = len(emails_content)
    
    logging.info("Iniciando envío de {} emails...".format(total_emails))
    
    for email, email_info in emails_content.items():
        try:
            # Determinar destinatario según modo
            if DEV_MODE:
                to_email = "<EMAIL>"
                logging.info("[DEV] Email para {} se envía a {}".format(email, to_email))
            else:
                to_email = email
                logging.info("[PROD] Email para {}".format(email))
            
            # Construir lista de BCC
            if DEV_MODE:
                # En modo desarrollo NO enviar a cuentas ni configuraciones
                bcc_list = []
            else:
                bcc_list = [PARATY_EPAYMENT_SUPERVISORS]
            
            # Eliminar duplicados y emails vacíos
            bcc_list = list(set([email for email in bcc_list if email and email.strip()]))
            
            # Enviar email usando Postmark
            postmark = PostmarkClient(server_token='************************************')
            
            response = postmark.emails.send(
                From='Sistema Monitoreo PEP_PAYLINKS <<EMAIL>>',
                To=to_email,
                Bcc=';'.join(bcc_list),
                Subject=email_info['subject'],
                HtmlBody=email_info['html_content'],
                TextBody=email_info['text_content']
            )
            
            emails_sent += 1
            logging.info("Email enviado a {} (BCC: {} destinatarios)".format(to_email, len(bcc_list)))
            
        except Exception as e:
            logging.error("Error enviando email a {}: {}".format(email, e), exc_info=True)
    
    logging.info("Envío completado: {}/{} emails enviados".format(emails_sent, total_emails))
    return emails_sent


def send_pep_paylinks_emails_with_conditions(emails_content, days=3, dev_mode=None):
    """
    Envía los emails con condiciones específicas de desarrollo.
    
    Args:
        emails_content (dict): Diccionario con emails como keys y contenido como value
        days (int): Número de días analizados
        dev_mode (bool): Si es True, cambia todos los <NAME_EMAIL>
        
    Returns:
        int: Número de emails enviados exitosamente
    """
    # Usar el parámetro dev_mode si se proporciona, sino usar la constante global
    is_dev = dev_mode if dev_mode is not None else DEV_MODE
    
    logging.info("MODO DE ENVÍO: {}".format('DESARROLLO' if is_dev else 'PRODUCCIÓN'))
    logging.info("PARÁMETROS: dev_mode={}, DEV_MODE={}, is_dev={}".format(dev_mode, DEV_MODE, is_dev))
    logging.info("TOTAL EMAILS A PROCESAR: {}".format(len(emails_content)))
    
    emails_sent = 0
    total_emails = len(emails_content)
    
    for email, email_info in emails_content.items():
        try:
            logging.info("PROCESANDO EMAIL: {} (is_dev={})".format(email, is_dev))
            
            # Determinar destinatario según modo
            if is_dev:
                to_email = "<EMAIL>"
                logging.info("[DEV] Email para {} → {}".format(email, to_email))
            else:
                to_email = email
                logging.info("[PROD] Email para {}".format(email))
            
            # Construir lista de BCC
            if is_dev:
                # En modo desarrollo NO enviar a cuentas ni configuraciones
                bcc_list = []
            else:
                bcc_list = [PARATY_EPAYMENT_SUPERVISORS]
            
            # Eliminar duplicados y emails vacíos
            bcc_list = list(set([bcc_email for bcc_email in bcc_list if bcc_email and bcc_email.strip()]))
            
            # Enviar email usando Postmark
            from postmarker.core import PostmarkClient
            postmark = PostmarkClient(server_token='************************************')
            
            # Log detallado ANTES del envío
            bcc_str = ';'.join(bcc_list)
            logging.info("ENVIANDO EMAIL:")
            logging.info("  - To: {}".format(to_email))
            logging.info("  - BCC: {}".format(bcc_str))
            logging.info("  - Subject: {}".format(email_info['subject']))
            logging.info("  - Modo Dev: {}".format(is_dev))
            
            response = postmark.emails.send(
                From='Sistema Monitoreo PEP_PAYLINKS <<EMAIL>>',
                To=to_email,
                Bcc=bcc_str,
                Subject=email_info['subject'],
                HtmlBody=email_info['html_content'],
                TextBody=email_info['text_content']
            )
            
            emails_sent += 1
            logging.info("✅ Email enviado EXITOSAMENTE a {} (BCC: {} destinatarios)".format(to_email, len(bcc_list)))
            
        except Exception as e:
            logging.error("Error enviando email a {}: {}".format(email, e), exc_info=True)
    
    logging.info("Envío completado: {}/{} emails enviados".format(emails_sent, total_emails))
    return emails_sent


if __name__ == "__main__":
    # Ejecución principal para producción - procesa todos los hoteles
    result_message, status_code = pep_paylinks_summary(filter_text="cambrils", days=3, dev_mode=True)
    logging.info("Proceso completado: {}".format(result_message))
