#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import json
import logging
import argparse
from datetime import datetime, timedelta
from collections import defaultdict
import xml.etree.ElementTree as ET

# Agregar Jinja2 para plantillas HTML
from jinja2 import Environment, FileSystemLoader

# Agregar el directorio src al Python path automáticamente
script_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(script_dir, '..', '..')
sys.path.insert(0, src_dir)

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_valid_hotels, get_account_manager
from postmarker.core import PostmarkClient
from paraty.development.development_utils import _get_total_payed_amount_from_all_sources, get_all_payments_info_list


def generate_mock_analysis_results():
    """
    Genera datos mock para testing sin consultar la base de datos real
    
    Returns:
        Dict con datos simulados del análisis
    """
    from datetime import datetime, timedelta
    
    now = datetime.now()
    start_date = (now - timedelta(days=3)).strftime("%Y-%m-%d")
    end_date = now.strftime("%Y-%m-%d")
    
    # Hoteles mock con diferentes escenarios y account managers
    mock_hotels = {
        'test-backend10': {  # <NAME_EMAIL>
            'hotel_code': 'test-backend10',
            'total_reservations': 15,
            'pending_reservations': 3,
            'should_be_cancelled': 2,
            'already_cancelled': 1,
            'fully_paid': 9,
            'reservations': [
                {
                    'identifier': 'Identifier 1',
                    'timestamp': '2024-01-15 10:30:00',
                    'startDate': '2024-02-01',
                    'endDate': '2024-02-03',
                    'email': '<EMAIL>',
                    'cancelled': False,
                    'status': 'pending',
                    'should_be_cancelled': True,
                    'reason': 'Payment link expired and no payment received',
                    'warnings': [],
                    'payment_link_expires': '2024-01-16 10:30:00',
                    'hours_until_expiry': -24.5,
                    'total_price': 150.00,
                    'total_paid': 0.00
                },
                {
                    'identifier': 'Identifier 2',
                    'timestamp': '2024-01-15 14:20:00',
                    'startDate': '2024-02-05',
                    'endDate': '2024-02-07',
                    'email': '<EMAIL>',
                    'cancelled': False,
                    'status': 'pending',
                    'should_be_cancelled': True,
                    'reason': 'Payment link expired and no payment received',
                    'warnings': [],
                    'payment_link_expires': '2024-01-16 14:20:00',
                    'hours_until_expiry': -20.3,
                    'total_price': 200.00,
                    'total_paid': 0.00
                }
            ]
        },
        'test-backend6': {  # Otro <NAME_EMAIL>
            'hotel_code': 'test-backend6',
            'total_reservations': 8,
            'pending_reservations': 1,
            'should_be_cancelled': 1,
            'already_cancelled': 0,
            'fully_paid': 7,
            'reservations': [
                {
                    'identifier': 'MOCK-003',
                    'timestamp': '2024-01-15 16:45:00',
                    'startDate': '2024-02-10',
                    'endDate': '2024-02-12',
                    'email': '<EMAIL>',
                    'cancelled': False,
                    'status': 'pending',
                    'should_be_cancelled': True,
                    'reason': 'Payment link expires in 5.2 hours',
                    'warnings': ['Payment link expires soon'],
                    'payment_link_expires': '2024-01-16 22:00:00',
                    'hours_until_expiry': 5.2,
                    'total_price': 180.00,
                    'total_paid': 0.00
                },
                {
                    'identifier': 'MOCK-004',
                    'timestamp': '2024-01-15 09:15:00',
                    'startDate': '2024-02-15',
                    'endDate': '2024-02-17',
                    'email': '<EMAIL>',
                    'cancelled': True,
                    'status': 'cancelled',
                    'should_be_cancelled': False,
                    'reason': 'Already cancelled',
                    'warnings': [],
                    'total_price': 120.00,
                    'total_paid': 0.00
                },
                {
                    'identifier': 'MOCK-005',
                    'timestamp': '2024-01-15 08:00:00',
                    'startDate': '2024-02-20',
                    'endDate': '2024-02-22',
                    'email': '<EMAIL>',
                    'cancelled': True,
                    'status': 'cancelled',
                    'should_be_cancelled': False,
                    'reason': 'Automatically cancelled yesterday',
                    'warnings': [],
                    'total_price': 180.00,
                    'total_paid': 0.00,
                    'incidents': 'Automatic cancelled',
                    'cancellationTimestamp': (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
                }
            ]
        },
        'hotel-mock-4': {  # Hotel sin reservas
            'hotel_code': 'hotel-mock-4',
            'total_reservations': 0,
            'pending_reservations': 0,
            'should_be_cancelled': 0,
            'already_cancelled': 0,
            'fully_paid': 0,
            'reservations': []
        },
        'hotel-mock-5': {  # Hotel sin configuración PEP_PAYLINKS
            'hotel_code': 'hotel-mock-5',
            'error': 'No PEP_PAYLINKS configuration',
            'total_reservations': 0,
            'pending_reservations': 0,
            'should_be_cancelled': 0,
            'already_cancelled': 0,
            'fully_paid': 0,
            'reservations': []
        },
        'hotel-success-1': {  # Hotel exitoso de otro account manager
            'hotel_code': 'hotel-success-1',
            'total_reservations': 8,
            'pending_reservations': 2,
            'should_be_cancelled': 0,
            'already_cancelled': 1,
            'fully_paid': 5,
            'reservations': [
                {
                    'identifier': 'SUCCESS-001',
                    'timestamp': '2024-01-15 11:00:00',
                    'startDate': '2024-02-20',
                    'endDate': '2024-02-22',
                    'email': '<EMAIL>',
                    'cancelled': False,
                    'status': 'confirmed',
                    'should_be_cancelled': False,
                    'reason': 'Fully paid - should not be cancelled',
                    'warnings': [],
                    'total_price': 250.00,
                    'total_paid': 250.00
                },
                {
                    'identifier': 'SUCCESS-002',
                    'timestamp': '2024-01-15 12:00:00',
                    'startDate': '2024-02-25',
                    'endDate': '2024-02-27',
                    'email': '<EMAIL>',
                    'cancelled': False,
                    'status': 'pending',
                    'should_be_cancelled': False,
                    'reason': 'Payment link expires in 15.5 hours',
                    'warnings': [],
                    'total_price': 180.00,
                    'total_paid': 0.00
                }
            ]
        },
        'hotel-success-2': {  # Otro hotel exitoso del mismo account manager que hotel-mock-3
            'hotel_code': 'hotel-success-2',
            'total_reservations': 7,
            'pending_reservations': 0,
            'should_be_cancelled': 0,
            'already_cancelled': 0,
            'cancelled_yesterday': 1,
            'fully_paid': 5,
            'reservations': [
                {
                    'identifier': 'SUCCESS-003',
                    'timestamp': '2024-01-15 13:00:00',
                    'startDate': '2024-03-01',
                    'endDate': '2024-03-03',
                    'email': '<EMAIL>',
                    'cancelled': False,
                    'status': 'confirmed',
                    'should_be_cancelled': False,
                    'reason': 'Fully paid - should not be cancelled',
                    'warnings': [],
                    'total_price': 300.00,
                    'total_paid': 300.00
                },
                {
                    'identifier': 'SUCCESS-004',
                    'timestamp': '2024-01-15 14:00:00',
                    'startDate': '2024-03-05',
                    'endDate': '2024-03-07',
                    'email': '<EMAIL>',
                    'cancelled': True,
                    'status': 'cancelled',
                    'should_be_cancelled': False,
                    'reason': 'Automatically cancelled yesterday',
                    'warnings': [],
                    'total_price': 220.00,
                    'total_paid': 0.00,
                    'incidents': 'Automatic cancelled',
                    'cancellationTimestamp': (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S")
                }
            ]
        },
        'hotel-with-emails-summary': {  # Hotel con configuración "emails to send summary"
            'hotel_code': 'hotel-with-emails-summary',
            'total_reservations': 5,
            'pending_reservations': 1,
            'should_be_cancelled': 1,
            'already_cancelled': 0,
            'fully_paid': 3,
            'emails_to_send_summary': ['<EMAIL>'],
            'reservations': [
                {
                    'identifier': 'EMAILS-001',
                    'timestamp': '2024-01-15 10:00:00',
                    'startDate': '2024-02-15',
                    'endDate': '2024-02-17',
                    'email': '<EMAIL>',
                    'cancelled': False,
                    'status': 'pending',
                    'should_be_cancelled': True,
                    'reason': 'Payment link expired and no payment received',
                    'warnings': [],
                    'payment_link_expires': '2024-01-16 10:00:00',
                    'hours_until_expiry': -12.5,
                    'total_price': 180.00,
                    'total_paid': 0.00
                }
            ]
        },
        'hotel-with-multiple-emails': {  # Hotel con múltiples emails en "emails to send summary"
            'hotel_code': 'hotel-with-multiple-emails',
            'total_reservations': 3,
            'pending_reservations': 0,
            'should_be_cancelled': 0,
            'already_cancelled': 0,
            'fully_paid': 3,
            'emails_to_send_summary': ['<EMAIL>', '<EMAIL>'],
            'reservations': [
                {
                    'identifier': 'MULTI-001',
                    'timestamp': '2024-01-15 11:00:00',
                    'startDate': '2024-02-20',
                    'endDate': '2024-02-22',
                    'email': '<EMAIL>',
                    'cancelled': False,
                    'status': 'confirmed',
                    'should_be_cancelled': False,
                    'reason': 'Fully paid - should not be cancelled',
                    'warnings': [],
                    'total_price': 250.00,
                    'total_paid': 250.00
                }
            ]
        }
    }
    
    # Definir las fechas para el período del reporte
    start_date = (now - timedelta(days=3)).strftime("%Y-%m-%d")
    end_date = now.strftime("%Y-%m-%d")
    
    return {
        'analysis_date': now.strftime("%Y-%m-%d %H:%M:%S"),
        'period': start_date + ' to ' + end_date,
        'hotels_analyzed': len(mock_hotels),
        'total_hotels_available': len(mock_hotels),
        'max_hotels_with_results': None,
        'early_stop': False,
        'hotels_with_results': 6,  # hotel-mock-5 no tiene resultados por error, pero agregamos 2 hoteles exitosos
        'hotels': mock_hotels
    }


# =============================================================================
# CONFIGURACIÓN DEL SCRIPT (explicación y valores actuales)
# =============================================================================
# SIMULATION_MODE: True = Solo envía emails a ti (modo simulación)
#                 False = Envía emails a todos los account managers (modo producción)
# Valor actual:
SIMULATION_MODE = False  # DEBUG: Modo simulación para debuggear

# SIMULATION_EMAIL: Email que recibirá todos los reportes en modo simulación
# Valor actual:
SIMULATION_EMAIL = "<EMAIL>"

# MOCK_RESULTS: True = Usar datos mock para testing (no consulta base de datos real)
#               False = Usar datos reales de la base de datos
# Valor actual:
MOCK_RESULTS = False

# FORCE_FIND_RESERVATION: True = Forzar búsqueda de una reserva específica
#                         False = Buscar reservas normalmente
# Valor actual:
FORCE_FIND_RESERVATION = False

# FORCE_HOTEL_CODE: Código de hotel a buscar si FORCE_FIND_RESERVATION está activo
# Valor actual:
FORCE_HOTEL_CODE = "test-backend10"

# FORCE_RESERVATION_ID: Identificador de reserva a buscar si FORCE_FIND_RESERVATION está activo
# Valor actual:
FORCE_RESERVATION_ID = "********"
# FORCE_RESERVATION_ID = None

# FORCE_ACCOUNT_MANAGER: Email específico del account manager para filtrar hoteles
# Si se especifica, solo se procesarán hoteles de este account manager
# Valor actual:
# FORCE_ACCOUNT_MANAGER = "<EMAIL>"
# FORCE_ACCOUNT_MANAGER = "<EMAIL>"

# HOTELS_TO_ANALYZE: Lista específica de hoteles a analizar
# Si se especifica, solo se procesarán estos hoteles
HOTELS_TO_ANALYZE = [
    "best-tenerife",
    "best-siroco", 
    "best-andorracenter",
    "best-maritim",
    "best-triton",
    "best-cambrils",
    "best-terramarina"
]

# START_DATE: Fecha desde la cual analizar reservas (formato YYYY-MM-DD)
# Si se especifica, se usará esta fecha en lugar de calcular días hacia atrás
START_DATE = "2025-07-01"

# PARATY_EPAYMENT_SUPERVISORS: Grupo de supervisores de epayment que debe recibir todos los emails
PARATY_EPAYMENT_SUPERVISORS = "<EMAIL>"
# =============================================================================


def get_mock_account_manager(hotel_code):
    """
    Función mock para simular get_account_manager en modo MOCK_RESULTS
    Simula diferentes account managers para probar la agrupación
    """
    # Mapeo de hoteles mock a account managers
    mock_account_managers = {
        'test-backend10': '<EMAIL>',
        'test-backend6': '<EMAIL>',
        'hotel-mock-3': '<EMAIL>',
        'hotel-success-1': '<EMAIL>',
        'hotel-success-2': '<EMAIL>',  # Mismo account manager que hotel-mock-3
        'hotel-with-emails-summary': '<EMAIL>',
        'hotel-with-multiple-emails': '<EMAIL>',
    }
    
    return mock_account_managers.get(hotel_code, '<EMAIL>')


def get_reservations_link_sent_not_paid(hotel_code, end_date, expire_hours_link):
    """
    Obtiene reservas donde se envió un link de pago pero no se ha pagado
    Busca en los pagos del tipo 'Envío de link al cliente' y verifica si las reservas correspondientes están pagadas
    """
    try:
        start_date = (datetime.now() - timedelta(hours=float(expire_hours_link) + 24)).strftime("%Y-%m-%d")
        payments = get_all_payments_info_list(
            [("timestamp", ">", start_date), ("timestamp", "<", end_date)], 
            hotel_code, 
            only_real_payments=False
        )
        
        reservations_link_sent_not_paid = []
        for payment in payments:
            if payment.get('type') == 'Envío de link al cliente':
                reservation = list(datastore_communicator.get_using_entity_and_params(
                    'Reservation', 
                    hotel_code=hotel_code, 
                    search_params=[('identifier', '=', payment.get('reservation_identifier'))]
                ))
                if reservation:
                    reservation = reservation[0]
                    extra_info = json.loads(reservation.get('extraInfo'))
                    if not fully_paid(reservation, extra_info):
                        reservations_link_sent_not_paid.append(reservation)
        
        return reservations_link_sent_not_paid
    except Exception as e:
        logging.warning("Error obteniendo reservas con link enviado no pagado para {}: {}".format(hotel_code, e))
        return []


def combine_lists(reservations, reservations_link_sent_not_paid):
    """
    Combina dos listas de reservas eliminando duplicados basándose en el identificador
    """
    combined_dict = {}

    # Primero agregar las reservas con link enviado no pagado
    for reservation in reservations_link_sent_not_paid:
        combined_dict[reservation.get('identifier')] = reservation

    # Luego agregar las reservas normales (pueden sobrescribir las anteriores)
    for reservation in reservations:
        combined_dict[reservation.get('identifier')] = reservation

    return list(combined_dict.values())



def get_expire_hours_link(pep_paylinks):
    """Obtiene las horas de expiración del link de pago"""
    for configs in pep_paylinks.get("configurations", []):
        if "expire_hours_link" in configs and (configs.split(" @@ ")[0] == "expire_hours_link"):
            return float(configs.split(" @@ ")[1])
    return 24  # Default


def get_limit_time_multibanco(pep_paylinks):
    """Obtiene el tiempo límite para multibanco desde la configuración SIBS"""
    for configs in pep_paylinks.get("configurations", []):
        if "limit_time_multibanco" in configs and (configs.split(" @@ ")[0] == "limit_time_multibanco"):
            return float(configs.split(" @@ ")[1])
    return 24  # Default


def get_automatic_cancel_pending(pep_paylinks):
    """Verifica si el hotel tiene cambio de estado automático habilitado"""
    for configs in pep_paylinks.get("configurations", []):
        if "automatic_cancell_pending" in configs:
            return configs.split(" @@ ")[1]
    return None


def get_hide_cancelled_yesterday_from_summary(pep_paylinks):
    """Busca la configuración summary y extrae el valor de hide_cancelled_yesterday si existe (string tipo key;key2;key3)."""
    for configs in pep_paylinks.get("configurations", []):
        if configs.startswith("summary @@ "):
            summary_str = configs.split(" @@ ", 1)[1]
            try:
                for part in summary_str.split(';'):
                    if part.strip() == 'hide_cancelled_yesterday':
                        return True
            except Exception as e:
                logging.warning("Error parsing summary string: {}".format(e))
    return False


def get_nothing_paid(extra_info):
    """Verifica si no se ha pagado nada en la reserva"""
    return not float(extra_info.get("payed", 0) or 0) and not extra_info.get("payed_by_tpv_link") and not extra_info.get('payed_by_cobrador')


def get_fixed_payment_link_send_date(reservation, expire_hours_link):
    """Calcula la fecha de expiración del link de pago"""
    try:
        extra_info = json.loads(reservation.get("extraInfo", "{}"))
        if extra_info.get("payment_link_send_date"):
            payment_link_send_date = datetime.strptime(extra_info.get("payment_link_send_date"), "%Y-%m-%d %H:%M:%S")
            return payment_link_send_date + timedelta(hours=expire_hours_link)
        
        # Si no hay fecha de envío del link, usar timestamp de la reserva
        initial_ts = reservation.get("modificationTimestamp") or reservation.get("timestamp", "")
        if initial_ts:
            return datetime.strptime(initial_ts, "%Y-%m-%d %H:%M:%S") + timedelta(hours=expire_hours_link)
    except Exception as e:
        logging.warning("Error calculating payment link expiry for {}: {}".format(reservation.get('identifier'), e))
    return None


def fully_paid(reservation, extra_info):
    """Verifica si la reserva está completamente pagada"""
    try:
        total_price = float(reservation.get("price", 0)) + float(reservation.get("priceSupplements", 0))
        total_paid = float(extra_info.get("payed", 0) or 0)
        
        if extra_info.get("payed_by_cobrador"):
            total_paid += float(extra_info.get("payed_by_cobrador", 0))
        
        if extra_info.get("payed_by_tpv_link"):
            for payment in extra_info.get("payed_by_tpv_link", []):
                total_paid += float(payment.get("amount", 0))
        
        return total_paid >= total_price
    except Exception:
        return False


def analyze_reservation_status(reservation, pep_configurations, hotel_code):
    """Analiza si una reserva debería estar cancelada"""
    identifier = reservation.get('identifier')
    extra_info = json.loads(reservation.get("extraInfo", "{}"))
    status_reservation = extra_info.get("status_reservation")
    
    analysis = {
        'identifier': identifier,
        'timestamp': reservation.get('timestamp'),
        'startDate': reservation.get('startDate'),
        'endDate': reservation.get('endDate'),
        'email': reservation.get('email', 'N/A'),
        'cancelled': reservation.get('cancelled', False),
        'status': status_reservation,
        'should_be_cancelled': False,
        'total_price': reservation.get('price', 0) + reservation.get('priceSupplements', 0),
        'total_paid': _get_total_payed_amount_from_all_sources(extra_info),
        'reason': '',
        'warnings': [],
        'cancelled_yesterday': False,
        'cancellationTimestamp': reservation.get('cancellationTimestamp')
    }
    
    # Si ya está cancelada, verificar si fue cancelada automáticamente ayer
    if reservation.get('cancelled'):
        # Verificar si tiene "Automatic cancelled" en incidents
        incidents = reservation.get('incidents', '') or ''
        if 'Automatic cancelled' in incidents:
            # Verificar si se canceló ayer
            try:
                cancellation_timestamp = reservation.get('cancellationTimestamp')
                if cancellation_timestamp:
                    cancellation_date = datetime.strptime(cancellation_timestamp, "%Y-%m-%d %H:%M:%S")
                    yesterday = datetime.now() - timedelta(days=1)
                    
                    # Verificar si se canceló ayer (mismo día)
                    if (cancellation_date.date() == yesterday.date()):
                        analysis['cancelled_yesterday'] = True
                        analysis['cancellationTimestamp'] = cancellation_timestamp
                        analysis['reason'] = 'Automatically cancelled yesterday'
                        return analysis
            except Exception as e:
                logging.warning("Error parsing cancellation timestamp for {}: {}".format(identifier, e))
        
            analysis['automatic_cancelled'] = True
            return analysis
        analysis['ignore'] = True
        return analysis
    
    # Verificar si está completamente pagada
    if fully_paid(reservation, extra_info):
        analysis['reason'] = 'Fully paid - should not be cancelled'
        return analysis
    
    # Solo analizar reservas pendientes
    if status_reservation != "pending":
        analysis['reason'] = 'Status is "{}" - not pending'.format(status_reservation)
        return analysis
    
    # Verificar configuración de cambio de estado automático
    if not pep_configurations.get('automatic_cancell_pending'):
        analysis['reason'] = 'Hotel does not have automatic status change enabled'
        return analysis
    
    # Verificar si no se ha pagado nada
    nothing_paid = get_nothing_paid(extra_info)
    if not nothing_paid:
        analysis['reason'] = 'Some payment detected'
        analysis['warnings'].append('Reservation has partial payment')
        return analysis
    
    # Verificar si es una reserva con SIBS_MULTIBANCO
    has_sibs_multibanco = extra_info.get('SIBS_MULTIBANCO') or (extra_info.get('SIBS') and extra_info.get('SIBS').get('MULTIBANCO'))
    
    # Determinar las horas de expiración según el tipo de pago
    if has_sibs_multibanco:
        # Para SIBS_MULTIBANCO, buscar la configuración limit_time_multibanco
        expire_hours_link = pep_configurations.get('limit_time_multibanco', 24)
        analysis['warnings'].append('SIBS_MULTIBANCO detected - using limit_time_multibanco')
    else:
        # Para otros tipos de pago, usar expire_hours_link normal
        expire_hours_link = pep_configurations.get('expire_hours_link', 24)
    
    expired_timestamp = get_fixed_payment_link_send_date(reservation, expire_hours_link)
    
    if not expired_timestamp:
        analysis['warnings'].append('Could not calculate payment link expiry date')
        analysis['reason'] = 'Cannot determine payment link expiry'
        return analysis
    
    # Agregar 12 horas adicionales al tiempo de expiración del payment link
    # antes de considerar que la reserva debería ser cancelada
    expiry_with_buffer = expired_timestamp + timedelta(hours=12)
    
    now = datetime.now()
    expired_link = expiry_with_buffer < now
    
    analysis['payment_link_expires'] = expired_timestamp.strftime("%Y-%m-%d %H:%M:%S")
    analysis['payment_link_expires_with_buffer'] = expiry_with_buffer.strftime("%Y-%m-%d %H:%M:%S")
    analysis['hours_until_expiry'] = (expired_timestamp - now).total_seconds() / 3600
    analysis['hours_until_expiry_with_buffer'] = (expiry_with_buffer - now).total_seconds() / 3600
    
    if expired_link:
        analysis['should_be_cancelled'] = True
        analysis['reason'] = 'Payment link expired and no payment received'
    else:
        hours_left = (expiry_with_buffer - now).total_seconds() / 3600
        analysis['reason'] = 'Payment link expires in {:.1f} hours'.format(hours_left)
        if hours_left < 2:
            analysis['warnings'].append('Payment link expires soon')
    
    return analysis


def get_emails_to_send_summary(pep_paylinks):
    """Obtiene los emails de 'emails to send summary' de la configuración PEP_PAYLINKS."""
    for configs in pep_paylinks.get("configurations", []):
        if configs.startswith("emails to send summary @@ "):
            emails_str = configs.split(" @@ ", 1)[1]
            try:
                # Separar emails por ; y limpiar espacios
                emails = [email.strip() for email in emails_str.split(';') if email.strip()]
                return emails
            except Exception as e:
                logging.warning("Error parsing 'emails to send summary' string: {}".format(e))
    return []


def get_pending_summaries_emails_from_advanced_config(hotel_code):
    """Obtiene los emails de 'pending summaries emails' de la configuración avanzada del hotel."""
    try:
        advanced_config = list(get_using_entity_and_params(
            "AdvancedConfiguration", 
            hotel_code=hotel_code, 
            return_cursor=True,
            search_params=[("name", "=", "pending summaries emails")]
        ))
        
        if advanced_config:
            config_value = advanced_config[0].get('value', '')
            if config_value:
                # Separar emails por ; y limpiar espacios
                emails = [email.strip() for email in config_value.split(';') if email.strip()]
                return emails
    except Exception as e:
        logging.warning("Error obteniendo configuración avanzada 'pending summaries emails' para {}: {}".format(hotel_code, e))
    return []


def check_reservations_should_be_cancelled(hotel_codes=None, days=3, max_hotels_with_results=None, account_manager_filter=None):
    """
    Verifica reservas de los últimos X días y determina cuáles deberían estar canceladas
    
    Args:
        hotel_codes: Lista de códigos de hotel o None para todos
        days: Número de días hacia atrás para buscar reservas
        max_hotels_with_results: Máximo número de hoteles con resultados a procesar (None = todos)
        account_manager_filter: Email del account manager para filtrar hoteles específicos
    
    Returns:
        Dict con el análisis de reservas por hotel
    """
    
    # DEBUG: Logging de configuración
    logging.info("🔧 DEBUG: MOCK_RESULTS = {}".format(MOCK_RESULTS))
    logging.info("🔧 DEBUG: SIMULATION_MODE = {}".format(SIMULATION_MODE))
    logging.info("🔧 DEBUG: SIMULATION_EMAIL = {}".format(SIMULATION_EMAIL))
    logging.info("🔧 DEBUG: FORCE_FIND_RESERVATION = {}".format(FORCE_FIND_RESERVATION))
    logging.info("🔧 DEBUG: HOTELS_TO_ANALYZE = {}".format(HOTELS_TO_ANALYZE))
    logging.info("🔧 DEBUG: START_DATE = {}".format(START_DATE))
    
    # Obtener hoteles a analizar
    if hotel_codes is None:
        # Usar lista específica de hoteles si está definida
        if 'HOTELS_TO_ANALYZE' in globals() and HOTELS_TO_ANALYZE:
            hotel_codes = HOTELS_TO_ANALYZE
        else:
            all_hotels = get_all_valid_hotels()
            hotel_codes = [hotel['applicationId'] for hotel in all_hotels]
    elif isinstance(hotel_codes, str):
        hotel_codes = [hotel_codes]
    
    # Filtrar por account manager si se especifica
    if account_manager_filter:
        filtered_hotel_codes = []
        for hotel_code in hotel_codes:
            try:
                if MOCK_RESULTS:
                    hotel_account_manager = get_mock_account_manager(hotel_code)
                else:
                    hotel_account_manager = get_account_manager(hotel_code)
                # Permitir múltiples emails separados por ;
                emails = [e.strip().lower() for e in str(hotel_account_manager).split(';')]
                if account_manager_filter.lower() in emails:
                    filtered_hotel_codes.append(hotel_code)
            except Exception as e:
                logging.warning("Error obteniendo account manager para {}: {}".format(hotel_code, e))
                continue
        hotel_codes = filtered_hotel_codes
        logging.info("Filtrado por account manager '{}': {} hoteles encontrados".format(account_manager_filter, len(hotel_codes)))
    
    # Usar fecha específica si está definida, sino calcular basado en días
    if 'START_DATE' in globals() and START_DATE:
        start_date = START_DATE
    else:
        start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")
    end_date = datetime.now().strftime("%Y-%m-%d")
    
    results = {
        'analysis_date': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'period': '{} to {}'.format(start_date, end_date),
        'hotels_analyzed': 0,  # Se actualizará al final
        'total_hotels_available': len(hotel_codes),
        'max_hotels_with_results': max_hotels_with_results,
        'early_stop': False,
        'hotels': {}
    }
    
    hotels_with_results_count = 0
    hotels_processed = 0
    
    for hotel_code in hotel_codes:
        hotels_processed += 1
        logging.info("🔍 DEBUG: Procesando hotel {} ({}/{})".format(hotel_code, hotels_processed, len(hotel_codes)))
        
        hotel_analysis = {
            'hotel_code': hotel_code,
            'total_reservations': 0,
            'pending_reservations': 0,
            'should_be_cancelled': 0,
            'already_cancelled': 0,
            'cancelled_yesterday': 0,
            'fully_paid': 0,
            'confirmed_by_link': 0,
            'reservations': [],
            'hide_cancelled_yesterday': False,
            'pending_summaries_emails': [],
            'emails_to_send_summary': []
        }
        
        # Obtener configuración PEP_PAYLINKS
        try:
            pep_paylinks = list(get_using_entity_and_params(
                "IntegrationConfiguration", 
                hotel_code=hotel_code, 
                return_cursor=True,
                search_params=[("name", "=", "PEP_PAYLINKS COBRADOR")]
            ))
            
            if not pep_paylinks:
                hotel_analysis['error'] = 'No PEP_PAYLINKS configuration'
                results['hotels'][hotel_code] = hotel_analysis
                continue
                
            pep_paylinks = pep_paylinks[0]
            
        except Exception as e:
            logging.warning("Error obteniendo configuración PEP para {}: {}".format(hotel_code, e))
            hotel_analysis['error'] = str(e)
            results['hotels'][hotel_code] = hotel_analysis
            continue
        
        # Configuraciones del hotel
        pep_configurations = {
            'expire_hours_link': get_expire_hours_link(pep_paylinks),
            'limit_time_multibanco': get_limit_time_multibanco(pep_paylinks),
            'automatic_cancell_pending': get_automatic_cancel_pending(pep_paylinks)
        }
        # Buscar hide_cancelled_yesterday en summary string
        hotel_analysis['hide_cancelled_yesterday'] = get_hide_cancelled_yesterday_from_summary(pep_paylinks)
        # Obtener emails de pending summaries
        hotel_analysis['pending_summaries_emails'] = get_pending_summaries_emails_from_advanced_config(hotel_code)
        # Obtener emails de 'emails to send summary'
        hotel_analysis['emails_to_send_summary'] = get_emails_to_send_summary(pep_paylinks)
        
        # Obtener reservas del período
        try:
            if FORCE_FIND_RESERVATION and hotel_code == FORCE_HOTEL_CODE:
                # Búsqueda específica para la reserva forzada
                reservations = list(get_using_entity_and_params(
                    "Reservation", 
                    hotel_code=hotel_code, 
                    return_cursor=True,
                    search_params=[
                        ("identifier", "=", FORCE_RESERVATION_ID)
                    ]
                ))
                
                if not reservations:
                    # Si no se encuentra por identifier, buscar en un rango más amplio
                    extended_start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
                    reservations = list(get_using_entity_and_params(
                        "Reservation", 
                        hotel_code=hotel_code, 
                        return_cursor=True,
                        search_params=[
                            ("timestamp", ">", extended_start_date), 
                            ("timestamp", "<", end_date)
                        ]
                    ))
                    
                    # Filtrar por identifier
                    reservations = [r for r in reservations if r.get('identifier') == FORCE_RESERVATION_ID]
            else:
                # Búsqueda normal
                reservations = list(get_using_entity_and_params(
                    "Reservation", 
                    hotel_code=hotel_code, 
                    return_cursor=True,
                    search_params=[
                        ("timestamp", ">", start_date), 
                        ("timestamp", "<", end_date)
                    ]
                ))
            
            if FORCE_FIND_RESERVATION and hotel_code == FORCE_HOTEL_CODE:
                if reservations:
                    pass # No print here
                else:
                    pass # No print here
            
            # Obtener también reservas con link enviado pero no pagado
            try:
                if not MOCK_RESULTS:  # Solo en modo real, no en mock
                    reservations_link_sent_not_paid = get_reservations_link_sent_not_paid(
                        hotel_code, 
                        end_date, 
                        pep_configurations['expire_hours_link']
                    )
                    reservations = combine_lists(reservations, reservations_link_sent_not_paid)
                    
                    if reservations_link_sent_not_paid:
                        logging.info("Hotel {}: Agregadas {} reservas con link enviado no pagado".format(hotel_code, len(reservations_link_sent_not_paid)))
            except Exception as e:
                logging.warning("Error obteniendo reservas con link enviado para {}: {}".format(hotel_code, e))
            
            hotel_analysis['total_reservations'] = len(reservations)
            logging.info("🔍 DEBUG: Hotel {} - {} reservas encontradas".format(hotel_code, len(reservations)))
            
            if not reservations:
                logging.info("🔍 DEBUG: Hotel {} - Sin reservas, saltando".format(hotel_code))
                results['hotels'][hotel_code] = hotel_analysis
                continue
            
        except Exception as e:
            logging.warning("Error obteniendo reservas: {}".format(e))
            hotel_analysis['error'] = str(e)
            results['hotels'][hotel_code] = hotel_analysis
            continue
        
        # Analizar cada reserva
        logging.info("🔍 DEBUG: Hotel {} - Analizando {} reservas".format(hotel_code, len(reservations)))
        for i, reservation in enumerate(reservations):
            if i < 3:  # Solo loggear las primeras 3 reservas para no saturar
                logging.info("🔍 DEBUG: Hotel {} - Reserva {}: {} - {}".format(hotel_code, i+1, reservation.get('identifier', 'N/A'), reservation.get('email', 'N/A')))
            
            analysis = analyze_reservation_status(reservation, pep_configurations, hotel_code)
            hotel_analysis['reservations'].append(analysis)

            if analysis.get('ignore', False):
                continue
            
            # Contar estadísticas (incluir reservas canceladas ayer)
            if analysis.get('cancelled_yesterday', False):
                hotel_analysis['cancelled_yesterday'] += 1
            elif analysis.get('automatic_cancelled', False):
                hotel_analysis['already_cancelled'] += 1
            elif analysis.get('cancelled', False):
                # Solo contar como ya canceladas las que NO fueron canceladas ayer
                hotel_analysis['already_cancelled'] += 1
            elif analysis.get('should_be_cancelled', False):
                hotel_analysis['should_be_cancelled'] += 1
                pass # No print here
            elif analysis.get('status', 'pending') == 'pending':
                hotel_analysis['pending_reservations'] += 1
            elif 'Fully paid' in analysis.get('reason', ''):
                hotel_analysis['fully_paid'] += 1
                # Verificar si fue pagada por link de pago
                extra_info = json.loads(reservation.get("extraInfo", "{}"))
                if extra_info.get("payed_by_tpv_link"):
                    hotel_analysis['confirmed_by_link'] += 1
        
        # Filtrar reservas: solo incluir las que deberían cancelarse O las canceladas ayer
        filtered_reservations = []
        for r in hotel_analysis['reservations']:
            should_include = False
            
            # Incluir si debería ser cancelada
            if r.get('should_be_cancelled', False):
                should_include = True
            # Incluir si fue cancelada ayer
            elif r.get('cancelled_yesterday', False):
                should_include = True
            # Excluir si tiene "Automatic cancelled" en incidents (ya cancelada)
            elif r.get('cancelled', False) and 'Automatic cancelled' in (r.get('incidents', '') or ''):
                should_include = False
            # Para otras reservas canceladas, verificar si no tienen "Automatic cancelled"
            elif r.get('cancelled', False):
                # Solo incluir si NO tiene "Automatic cancelled" en incidents
                incidents = r.get('incidents', '') or ''
                if 'Automatic cancelled' not in incidents:
                    should_include = True
            
            if should_include:
                filtered_reservations.append(r)
        
        hotel_analysis['reservations'] = filtered_reservations
        
        # Resumen del hotel
        results['hotels'][hotel_code] = hotel_analysis
        
        # DEBUG: Logging del resumen del hotel
        logging.info("🔍 DEBUG: Hotel {} - Resumen:".format(hotel_code))
        logging.info("   - Total reservas: {}".format(hotel_analysis['total_reservations']))
        logging.info("   - Deberían cancelarse: {}".format(hotel_analysis['should_be_cancelled']))
        logging.info("   - Canceladas ayer: {}".format(hotel_analysis['cancelled_yesterday']))
        logging.info("   - Ya canceladas: {}".format(hotel_analysis['already_cancelled']))
        logging.info("   - Completamente pagadas: {}".format(hotel_analysis['fully_paid']))
        logging.info("   - Pendientes OK: {}".format(hotel_analysis['pending_reservations']))
        
        # Verificar si este hotel tiene resultados relevantes
        has_results = (hotel_analysis['total_reservations'] > 0 and 
                      not hotel_analysis.get('error'))
        
        if has_results:
            hotels_with_results_count += 1
            
            # Verificar si alcanzamos el límite
            if max_hotels_with_results and hotels_with_results_count >= max_hotels_with_results:
                results['early_stop'] = True
                break
    
    # Actualizar estadísticas finales
    results['hotels_analyzed'] = hotels_processed
    results['hotels_with_results'] = hotels_with_results_count
    
    # Mensaje final sobre la limitación
    if results['early_stop']:
        pass # No print here
    
    return results


def save_analysis_report(analysis_results, filename=None):
    """Guarda el reporte de análisis en un archivo JSON"""
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = "pending_cancellation_analysis_{}.json".format(timestamp)
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    
    return filename





def group_hotels_by_account_manager(analysis_results):
    """
    Agrupa los hoteles por account manager y emails de 'emails to send summary' para enviar emails separados
    Solo incluye hoteles que tienen PEP_PAYLINKS COBRADOR configurado
    
    Args:
        analysis_results: Resultados del análisis de reservas
    
    Returns:
        Dict con account managers como keys y sus hoteles como values
    """
    account_manager_hotels = defaultdict(dict)
    
    for hotel_code, hotel_data in analysis_results['hotels'].items():
        # Solo incluir hoteles que no tienen error de configuración PEP_PAYLINKS
        if hotel_data.get('error') == 'No PEP_PAYLINKS configuration':
            continue
            
        try:
            # Obtener account manager directamente
            if MOCK_RESULTS:
                account_manager_email = get_mock_account_manager(hotel_code)
            else:
                account_manager_email = get_account_manager(hotel_code)
            
            # En modo simulación, todos los hoteles van al email de simulación
            if SIMULATION_MODE:
                account_manager_email = SIMULATION_EMAIL
            
            # Agrupar el hotel bajo su account manager
            account_manager_hotels[account_manager_email][hotel_code] = hotel_data
            
            # También agrupar por emails de 'emails to send summary' si están configurados
            emails_to_send_summary = hotel_data.get('emails_to_send_summary', [])
            for email in emails_to_send_summary:
                if email.strip():  # Solo agregar si el email no está vacío
                    # En modo simulación, usar email de simulación
                    if SIMULATION_MODE:
                        email_key = SIMULATION_EMAIL
                    else:
                        email_key = email.strip()
                    
                    # Agrupar el hotel bajo este email también
                    account_manager_hotels[email_key][hotel_code] = hotel_data
            
        except Exception as e:
            logging.warning("Error obteniendo account manager para {}: {}".format(hotel_code, e))
            # Si no se puede obtener el account manager, usar email por defecto
            default_email = SIMULATION_EMAIL if SIMULATION_MODE else "<EMAIL>"
            account_manager_hotels[default_email][hotel_code] = hotel_data
    
    return account_manager_hotels


def generate_html_report_for_account_manager(account_manager_email, hotels_data, analysis_results, simulation_info=None):
    """Genera un reporte HTML específico para un account manager usando una plantilla externa Jinja2."""
    env = Environment(
        loader=FileSystemLoader(os.path.join(os.path.dirname(__file__), 'emails', 'output_emails')),
        autoescape=True
    )
    template = env.get_template('report_pending_should_be_cancelled_account_manager.html')
    html_content = template.render(
        account_manager_email=account_manager_email,
        hotels_data=hotels_data,
        analysis_results=analysis_results,
        simulation_info=simulation_info
    )
    return html_content


def generate_simulation_mapping_report(account_manager_hotels):
    """
    Genera un reporte de simulación que muestra qué hoteles se enviarán a cada email
    
    Args:
        account_manager_hotels: Dict con emails como keys y hoteles como values
    
    Returns:
        String con el reporte de mapeo
    """
    report_lines = []
    report_lines.append("📧 SIMULACIÓN: MAPEO DE EMAILS A HOTELES")
    report_lines.append("=" * 50)
    report_lines.append("")
    
    # Ordenar por email para mejor legibilidad
    sorted_emails = sorted(account_manager_hotels.keys())
    
    for email in sorted_emails:
        hotels = account_manager_hotels[email]
        hotel_codes = sorted(hotels.keys())
        
        report_lines.append("📧 {}".format(email))
        report_lines.append("   Hoteles ({}): {}".format(len(hotel_codes), ', '.join(hotel_codes)))
        
        # Mostrar estadísticas de este email
        total_should_cancel = sum(hotels[hotel].get('should_be_cancelled', 0) for hotel in hotels)
        total_reservations = sum(hotels[hotel].get('total_reservations', 0) for hotel in hotels)
        
        report_lines.append("   - Total reservas: {}".format(total_reservations))
        report_lines.append("   - Deberían cancelarse: {}".format(total_should_cancel))
        report_lines.append("")
    
    # Resumen final
    total_emails = len(account_manager_hotels)
    total_hotels = sum(len(hotels) for hotels in account_manager_hotels.values())
    
    report_lines.append("📊 RESUMEN:")
    report_lines.append("   - Total emails: {}".format(total_emails))
    report_lines.append("   - Total hoteles: {}".format(total_hotels))
    report_lines.append("")
    report_lines.append("💡 NOTA: En modo simulación, todos los emails se enví<NAME_EMAIL>")
    
    return "\n".join(report_lines)


def send_email_report(analysis_results, recipient_email="<EMAIL>", account_manager_filter=None):
    """Envía el reporte por email usando el sistema de Paraty"""
    try:
        # Usar la función group_hotels_by_account_manager para agrupar correctamente
        account_manager_hotels = group_hotels_by_account_manager(analysis_results)
        
        # Filtrar por account manager específico si se especifica
        if account_manager_filter:
            filtered_hotels = {}
            if account_manager_filter in account_manager_hotels:
                filtered_hotels[account_manager_filter] = account_manager_hotels[account_manager_filter]
            account_manager_hotels = filtered_hotels
            logging.info("Enviando email solo a account manager: {}".format(account_manager_filter))
        
        # En modo simulación, generar y enviar reporte de mapeo
        if SIMULATION_MODE:
            mapping_report = generate_simulation_mapping_report(account_manager_hotels)
            
            # Enviar email de simulación con el mapeo
            subject = "[SIMULACIÓN] Mapeo de emails a hoteles - {}".format(analysis_results['period'])
            
            html_content = """
            <html>
            <body>
                <h2>📧 SIMULACIÓN: MAPEO DE EMAILS A HOTELES</h2>
                <p><strong>Período:</strong> {}</p>
                <p><strong>Fecha análisis:</strong> {}</p>
                <hr>
                <pre style="font-family: monospace; background-color: #f5f5f5; padding: 15px; border-radius: 5px;">
{}
                </pre>
                <hr>
                <p><em>Este es un reporte de simulación. En modo producción, cada email recibiría solo sus hoteles correspondientes.</em></p>
            </body>
            </html>
            """.format(analysis_results['period'], analysis_results['analysis_date'], mapping_report)
            
            text_content = """
SIMULACIÓN: MAPEO DE EMAILS A HOTELES
=====================================
Período: {}
Fecha análisis: {}

{}

---
Este es un reporte de simulación. En modo producción, cada email recibiría solo sus hoteles correspondientes.
            """.format(analysis_results['period'], analysis_results['analysis_date'], mapping_report)
            
            # Enviar email de simulación solo a ti y al grupo de supervisores
            postmark = PostmarkClient(server_token='************************************')
            # noinspection PyUnresolvedReferences
            response = postmark.emails.send(
                From='Sistema Monitoreo <<EMAIL>>',
                To=SIMULATION_EMAIL,
                Subject=subject,
                HtmlBody=html_content,
                TextBody=text_content
            )
            
            logging.info("📧 Email de simulación enviado a {} y BCC a {}".format(SIMULATION_EMAIL, PARATY_EPAYMENT_SUPERVISORS))
            logging.info("📧 Response de Postmark: {}".format(response))
            return True
        
        stats = calculate_summary_statistics(analysis_results)
        emails_sent = 0
        total_emails = len(account_manager_hotels)
        
        for account_manager_email, hotels_data in account_manager_hotels.items():
            try:
                # Obtener estadísticas para este account manager
                account_stats = stats['account_manager_stats'].get(account_manager_email, {
                    'hotels_count': 0,
                    'should_cancel': 0,
                    'fully_paid': 0,
                    'pending_ok': 0,
                    'already_cancelled': 0,
                    'cancelled_yesterday': 0
                })
                
                # En modo simulación, pasar el destinatario real a la plantilla
                simulation_info = None
                if SIMULATION_MODE:
                    simulation_info = account_manager_email
                    email_to_send = SIMULATION_EMAIL
                else:
                    email_to_send = account_manager_email
                
                html_content = generate_html_report_for_account_manager(
                    account_manager_email,
                    hotels_data,
                    analysis_results,
                    simulation_info=simulation_info
                )
                
                total_should_cancel = account_stats['should_cancel']
                total_hotels = account_stats['hotels_count']
                
                subject = "Reporte de reservas pendientes de pago {}".format(analysis_results['period'])
                
                text_content = """
REPORTE DE RESERVAS PENDIENTES DE CAMBIO DE ESTADO - TUS HOTELES
============================================================
Account Manager: {}
Período: {}
Fecha análisis: {}
RESUMEN DE TUS HOTELES:
- Hoteles analizados: {}
- Reservas que requieren cambio de estado: {}
- Reservas pendientes (normales): {}
- Reservas canceladas automáticamente ayer: {}
- Reservas ya canceladas: {}
""".format(account_manager_email, analysis_results['period'], analysis_results['analysis_date'], total_hotels, total_should_cancel, account_stats.get('pending_ok', 0), account_stats.get('cancelled_yesterday', 0), account_stats.get('already_cancelled', 0))
                if SIMULATION_MODE:
                    text_content += "\n\n[SIMULACIÓN] Este email solo se envía a {}, pero habría sido enviado a: {}\n".format(SIMULATION_EMAIL, account_manager_email)
                text_content += "\nPara ver el reporte completo, consulte la versión HTML de este email.\n\nParaty Tech - Sistema de Monitoreo"
                
                # Recopilar todos los emails de pending summaries de los hoteles de este account manager
                pending_summaries_emails = []
                for hotel_code, hotel_data in hotels_data.items():
                    if hotel_data.get('pending_summaries_emails'):
                        pending_summaries_emails.extend(hotel_data['pending_summaries_emails'])
                
                # Recopilar todos los emails de 'emails to send summary' de los hoteles de este account manager
                emails_to_send_summary = []
                for hotel_code, hotel_data in hotels_data.items():
                    if hotel_data.get('emails_to_send_summary'):
                        emails_to_send_summary.extend(hotel_data['emails_to_send_summary'])
                
                # Eliminar duplicados y emails vacíos
                pending_summaries_emails = list(set([email for email in pending_summaries_emails if email]))
                emails_to_send_summary = list(set([email for email in emails_to_send_summary if email]))
                
                # Construir lista de BCC
                bcc_list = [
                    PARATY_EPAYMENT_SUPERVISORS
                ]
                if pending_summaries_emails:
                    bcc_list.extend(pending_summaries_emails)
                if emails_to_send_summary:
                    bcc_list.extend(emails_to_send_summary)
                
                postmark = PostmarkClient(server_token='************************************')
                # noinspection PyUnresolvedReferences
                postmark.emails.send(
                    From='Sistema Monitoreo <<EMAIL>>',
                    #To=email_to_send,
                    To="<EMAIL>",
                    Bcc=';'.join(bcc_list),
                    Subject=subject,
                    HtmlBody=html_content,
                    TextBody=text_content
                )
                emails_sent += 1
                
            except Exception as e:
                logging.error("Error enviando email a {}: {}".format(account_manager_email, e), exc_info=True)
        
        return emails_sent > 0
    except Exception as e:
        logging.error("Error general enviando emails: {}".format(e), exc_info=True)
        return False


def calculate_summary_statistics(analysis_results):
    """
    Calcula todas las estadísticas del análisis de una vez para optimizar rendimiento
    
    Args:
        analysis_results: Resultados del análisis de reservas
    
    Returns:
        Dict con todas las estadísticas calculadas
    """
    stats = {
        'total_hotels': analysis_results['hotels_analyzed'],
        'total_hotels_available': analysis_results.get('total_hotels_available', analysis_results['hotels_analyzed']),
        'hotels_with_results': analysis_results.get('hotels_with_results', 0),
        'early_stop': analysis_results.get('early_stop', False),
        'max_limit': analysis_results.get('max_hotels_with_results'),
        'total_should_cancel': 0,
        'total_reservations': 0,
        'total_fully_paid': 0,
        'total_pending_ok': 0,
        'total_already_cancelled': 0,
        'total_cancelled_yesterday': 0,
        'hotels_without_pep': 0,
        'account_manager_stats': {},
        'problematic_hotels': [],
        'successful_hotels': []
    }
    
    # Calcular todas las estadísticas en una sola pasada
    for hotel_code, hotel_data in analysis_results['hotels'].items():
        # Estadísticas generales
        stats['total_should_cancel'] += hotel_data.get('should_be_cancelled', 0)
        stats['total_reservations'] += hotel_data.get('total_reservations', 0)
        stats['total_fully_paid'] += hotel_data.get('fully_paid', 0)
        stats['total_pending_ok'] += hotel_data.get('pending_reservations', 0)
        stats['total_already_cancelled'] += hotel_data.get('already_cancelled', 0)
        stats['total_cancelled_yesterday'] += hotel_data.get('cancelled_yesterday', 0)
        
        # Contar hoteles sin PEP_PAYLINKS
        if hotel_data.get('error') == 'No PEP_PAYLINKS configuration':
            stats['hotels_without_pep'] += 1
            continue
        
        # Obtener account manager directamente
        try:
            if MOCK_RESULTS:
                account_manager_email = get_mock_account_manager(hotel_code)
            else:
                account_manager_email = get_account_manager(hotel_code)
        
            # En modo simulación, usar email de simulación
            if SIMULATION_MODE:
                account_manager_email = SIMULATION_EMAIL
            
            # Acumular estadísticas por account manager
            if account_manager_email not in stats['account_manager_stats']:
                stats['account_manager_stats'][account_manager_email] = {
                    'hotels_count': 0,
                    'should_cancel': 0,
                    'fully_paid': 0,
                    'pending_ok': 0,
                    'already_cancelled': 0,
                    'cancelled_yesterday': 0
                }
            
            stats['account_manager_stats'][account_manager_email]['hotels_count'] += 1
            stats['account_manager_stats'][account_manager_email]['should_cancel'] += hotel_data.get('should_be_cancelled', 0)
            stats['account_manager_stats'][account_manager_email]['fully_paid'] += hotel_data.get('fully_paid', 0)
            stats['account_manager_stats'][account_manager_email]['pending_ok'] += hotel_data.get('pending_reservations', 0)
            stats['account_manager_stats'][account_manager_email]['already_cancelled'] += hotel_data.get('already_cancelled', 0)
            stats['account_manager_stats'][account_manager_email]['cancelled_yesterday'] += hotel_data.get('cancelled_yesterday', 0)
            
            # También acumular estadísticas por emails de 'emails to send summary'
            emails_to_send_summary = hotel_data.get('emails_to_send_summary', [])
            for email in emails_to_send_summary:
                if email.strip():
                    # En modo simulación, usar email de simulación
                    if SIMULATION_MODE:
                        email_key = SIMULATION_EMAIL
                    else:
                        email_key = email.strip()
                    
                    # Acumular estadísticas por este email
                    if email_key not in stats['account_manager_stats']:
                        stats['account_manager_stats'][email_key] = {
                            'hotels_count': 0,
                            'should_cancel': 0,
                            'fully_paid': 0,
                            'pending_ok': 0,
                            'already_cancelled': 0,
                            'cancelled_yesterday': 0
                        }
                    
                    stats['account_manager_stats'][email_key]['hotels_count'] += 1
                    stats['account_manager_stats'][email_key]['should_cancel'] += hotel_data.get('should_be_cancelled', 0)
                    stats['account_manager_stats'][email_key]['fully_paid'] += hotel_data.get('fully_paid', 0)
                    stats['account_manager_stats'][email_key]['pending_ok'] += hotel_data.get('pending_reservations', 0)
                    stats['account_manager_stats'][email_key]['already_cancelled'] += hotel_data.get('already_cancelled', 0)
                    stats['account_manager_stats'][email_key]['cancelled_yesterday'] += hotel_data.get('cancelled_yesterday', 0)
            
            # Hoteles problemáticos
            if hotel_data.get('should_be_cancelled', 0) > 0:
                stats['problematic_hotels'].append({
                    'hotel_code': hotel_code,
                    'account_manager': account_manager_email,
                    'should_cancel': hotel_data.get('should_be_cancelled', 0),
                    'reservations': hotel_data.get('reservations', [])
                })
            
            # Hoteles exitosos (sin problemas)
            if hotel_data.get('should_be_cancelled', 0) == 0 and hotel_data.get('total_reservations', 0) > 0:
                stats['successful_hotels'].append({
                    'hotel_code': hotel_code,
                    'account_manager': account_manager_email,
                    'total_reservations': hotel_data.get('total_reservations', 0),
                    'fully_paid': hotel_data.get('fully_paid', 0),
                    'pending_ok': hotel_data.get('pending_reservations', 0),
                    'already_cancelled': hotel_data.get('already_cancelled', 0),
                    'cancelled_yesterday': hotel_data.get('cancelled_yesterday', 0)
                })
                
        except Exception as e:
            logging.warning("Error obteniendo account manager para {}: {}".format(hotel_code, e))
            # Usar email por defecto
            default_email = SIMULATION_EMAIL if SIMULATION_MODE else "<EMAIL>"
            if default_email not in stats['account_manager_stats']:
                stats['account_manager_stats'][default_email] = {
                    'hotels_count': 0,
                    'should_cancel': 0,
                    'fully_paid': 0,
                    'pending_ok': 0,
                    'already_cancelled': 0,
                    'cancelled_yesterday': 0
                }
            stats['account_manager_stats'][default_email]['hotels_count'] += 1
            stats['account_manager_stats'][default_email]['should_cancel'] += hotel_data.get('should_be_cancelled', 0)
            stats['account_manager_stats'][default_email]['fully_paid'] += hotel_data.get('fully_paid', 0)
            stats['account_manager_stats'][default_email]['pending_ok'] += hotel_data.get('pending_reservations', 0)
            stats['account_manager_stats'][default_email]['already_cancelled'] += hotel_data.get('already_cancelled', 0)
            stats['account_manager_stats'][default_email]['cancelled_yesterday'] += hotel_data.get('cancelled_yesterday', 0)
    
    return stats


def print_summary_report(analysis_results):
    """Imprime un resumen ejecutivo del análisis"""
    
    # Calcular todas las estadísticas de una vez
    stats = calculate_summary_statistics(analysis_results)
    
    # No print here


if __name__ == "__main__":
    # Configurar argumentos de línea de comandos
    parser = argparse.ArgumentParser(description='Analiza reservas que deberían estar canceladas')
    parser.add_argument('--days', type=int, default=3, help='Número de días hacia atrás para analizar (default: 3)')
    parser.add_argument('--max-hotels', type=int, help='Máximo número de hoteles con resultados a procesar (para testing)')
    parser.add_argument('--hotels', nargs='+', help='Lista específica de hoteles a analizar')
    parser.add_argument('--account-manager', type=str, help='Email del account manager para filtrar hoteles específicos')
    
    args = parser.parse_args()
    
    # Configurar logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        # Usar account manager de línea de comandos si se especifica, sino usar el de configuración
        account_manager_filter = args.account_manager if args.account_manager else None
        
        # Verificar si usar datos mock
        if MOCK_RESULTS:
            analysis_results = generate_mock_analysis_results()
        else:
            # ⚙️ CONFIGURACIÓN DEL ANÁLISIS
            # Usar hoteles específicos de configuración o argumentos de línea de comandos
            if 'HOTELS_TO_ANALYZE' in globals() and HOTELS_TO_ANALYZE:
                hotel_codes = HOTELS_TO_ANALYZE
            else:
                hotel_codes = args.hotels  # Usar argumentos de línea de comandos o None para todos
            
            # Si está activada la búsqueda forzada, usar solo ese hotel
            if FORCE_FIND_RESERVATION:
                hotel_codes = [FORCE_HOTEL_CODE]
            
            # Configurar límite para testing rápido (None = sin límite)
            max_hotels_with_results = args.max_hotels
            
            # Analizar reservas desde la fecha específica o los últimos X días
            analysis_results = check_reservations_should_be_cancelled(
                hotel_codes=hotel_codes, 
                days=args.days,
                max_hotels_with_results=max_hotels_with_results,
                account_manager_filter=account_manager_filter
            )
        
        # Guardar reporte local
        #report_file = save_analysis_report(analysis_results)
        
        # Enviar reporte por email
        email_sent = send_email_report(analysis_results, recipient_email="<EMAIL>", account_manager_filter=account_manager_filter)
        
        if email_sent:
            pass # No print here
        else:
            pass # No print here
        
        # Mostrar resumen de account managers usando estadísticas ya calculadas
        stats = calculate_summary_statistics(analysis_results)
        
        if SIMULATION_MODE:
            pass # No print here
        else:
            pass # No print here
        
    except Exception as e:
        logging.error("Error durante el análisis: {}".format(e), exc_info=True)
