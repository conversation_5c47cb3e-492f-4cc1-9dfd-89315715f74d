from paraty_commons_3.datastore import datastore_communicator

def get_booking_request_logs_for_admin_hotel():
    """
    Fetches BookingRequestLog entities from the 'admin-hotel' namespace in Google Datastore.
    """
    return datastore_communicator.get_using_entity_and_params('BookingRequestLog', hotel_code='admin-hotel')

if __name__ == '__main__':
    print("Fetching BookingRequestLog entities from namespace 'admin-hotel'...")
    logs = get_booking_request_logs_for_admin_hotel()
    if logs:
        print(f"Found {len(logs)} logs.")
        for log in logs:
            print(log)
    else:
        print("No BookingRequestLog entities found for 'admin-hotel'.")
