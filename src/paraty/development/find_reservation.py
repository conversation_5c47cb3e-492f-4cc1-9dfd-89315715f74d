import os

from paraty_commons_3.common_data import common_data_provider
from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.hotel_manager import hotel_manager_utils
from datetime import datetime
import csv


def get_reservations(hotel_code):
    return datastore_communicator.get_using_entity_and_params('Reservation', hotel_code=hotel_code)


def find_by_agent(reservation, agent_name):
    if reservation.get('agent') == agent_name:
        print(
            f"Identificador: {reservation.get('identifier')} Fecha de entrada: {reservation.get('startDate')} Fecha de salida: {reservation.get('endDate')}")
        return True

    return False


def find_confirmed_or_modified_for_hotel_in_period(hotel, min_date, max_date):

    all_reservations = common_data_provider.get_reservations_of_hotel(hotel, min_date, max_date, include_modified_reservations=True)

    return all_reservations



def find_by_date(reservation, start_date, end_date):
    s_date = datetime.strptime(start_date, '%Y-%m-%d')
    e_date = datetime.strptime(end_date, '%Y-%m-%d')
    c_date = datetime.strptime(reservation.get('timestamp'), '%Y-%m-%d %H:%M:%S')

    if s_date <= c_date <= e_date:
        print(
            f"Identificador: {reservation.get('identifier')} Fecha de creación: {reservation.get('timestamp')} Fecha de entrada: {reservation.get('startDate')} Fecha de salida: {reservation.get('endDate')}")

        return True

    return False


def to_csv(reservations, headers, elements):
    desktop = os.path.join(os.path.join(os.path.expanduser('~')), 'Desktop')
    file_name = 'Reservations.csv'
    f = open(os.path.join(desktop, file_name), "w")
    csv_writer = csv.writer(f)
    csv_writer.writerow(headers)

    for reservation in reservations:
        csv_writer.writerow([reservation.get(x) for x in elements])


def find_by_agent_cancelled_or_modified(reservation, agent_name, date):
    if reservation.get('agent') is not None:
        date = datetime.strptime(date, '%Y-%m-%d')

        if reservation.get('agent') == agent_name and reservation.get('cancelled'):
            cancellation_date = datetime.strptime(reservation.get('cancellationTimestamp'), '%Y-%m-%d %H:%M:%S')

            if cancellation_date >= date:
                print(
                    f"Identificador: {reservation.get('identifier')} Fecha de cancelación: {reservation.get('cancellationTimestamp')} Fecha de entrada: {reservation.get('startDate')} Fecha de salida: {reservation.get('endDate')}")

                return True

        elif reservation.get('agent') == agent_name and reservation.get('modificationTimestamp') is not None:
            modification_date = datetime.strptime(reservation.get('modificationTimestamp'), '%Y-%m-%d %H:%M:%S')

            if modification_date >= date:
                print(
                    f"Identificador: {reservation.get('identifier')} Fecha de modificación: {reservation.get('modificationTimestamp')} Fecha de entrada: {reservation.get('startDate')} Fecha de salida: {reservation.get('endDate')}")

                return True

    return False


def _to_csv(hotel_application, reservations, headers, elements):
    desktop = os.path.join(os.path.join(os.path.expanduser('~')), 'Desktop')
    file_name = f'{hotel_application}.csv'
    f = open(os.path.join('/tmp/best', file_name), "w")
    csv_writer = csv.writer(f)
    csv_writer.writerow(headers)

    for reservation in reservations:
        csv_writer.writerow([reservation.get(x) for x in elements])


if __name__ == '__main__':
    all_hotels = hotel_manager_utils.get_all_valid_hotels()
    r_list = []

    min_date = '2022-07-22 12:00:00'
    max_date = '2022-07-22 15:00:00'

    full_list = {}

    for hotel in all_hotels:

        if 'best' in hotel['applicationId']:
            print(hotel['applicationId'])

            reservation_list = find_confirmed_or_modified_for_hotel_in_period(hotel, min_date, max_date)

            # reservation_list = [reservation for reservation in get_reservations(hotel['applicationId']) if
            #                     find_by_agent_cancelled_or_modify(reservation, 'juani-best', '2022-06-13')]

            full_list[hotel['applicationId']] = reservation_list

    for k,v in full_list.items():
        if v:
            _to_csv(k, v, ['Identificador', 'timestamp', 'modificacion', 'Fecha de entrada', 'Fecha de salida', 'precio'], ['identifier', 'timestamp', 'modificationTimestamp', 'startDate', 'endDate', 'price'])
