<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reporte de Reservas - {{ account_manager_email }}</title>
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif; background-color: #f8f9fa; margin: 0; padding: 20px; line-height: 1.6;">
    
    <!-- Container principal -->
    <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 800px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 1px solid #e9ecef;">
        <tr>
            <td style="padding: 32px;">
                
                <!-- Header -->
                <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 32px;">
                    <tr>
                        <td style="text-align: center; padding-bottom: 24px; border-bottom: 2px solid #f1f3f4;">
                            <h1 style="font-size: 24px; font-weight: 600; color: #1a1a1a; margin: 0 0 8px 0;">📊 Reporte de Reservas Pendientes</h1>
                            <p style="color: #6c757d; margin: 4px 0; font-size: 14px;">Account Manager: <strong>{{ account_manager_email }}</strong></p>
                            <p style="color: #6c757d; margin: 4px 0; font-size: 14px;">Período: {{ analysis_results.period }}</p>
                            <p style="color: #6c757d; margin: 4px 0; font-size: 14px;">Fecha análisis: {{ analysis_results.analysis_date }}</p>
                            
                            {% set total_should_cancel = hotels_data.values()|map(attribute='should_be_cancelled')|sum %}
                            {% if total_should_cancel > 0 %}
                                <div style="display: inline-block; padding: 8px 16px; background-color: #dc3545; color: white; border-radius: 4px; font-weight: 500; margin-top: 12px;">
                                    ⚠️ ACCIÓN REQUERIDA
        </div>
                            {% else %}
                                <div style="display: inline-block; padding: 8px 16px; background-color: #28a745; color: white; border-radius: 4px; font-weight: 500; margin-top: 12px;">
                                    ✅ TODO CORRECTO
                                </div>
                            {% endif %}
                        </td>
                    </tr>
                </table>

                <!-- KPIs Section -->
                <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 32px;">
                    <tr>
                        <td style="background-color: #f8f9fa; border-radius: 8px; padding: 24px;">
                            <h2 style="font-size: 18px; font-weight: 600; color: #1a1a1a; margin: 0 0 20px 0; text-align: center;">📈 Resumen Ejecutivo</h2>
                            
                            <!-- KPIs Row -->
                            <table width="100%" cellpadding="0" cellspacing="0">
                                <tr>
                                    {% set kpis = [
                                        {'label': 'Hoteles', 'value': hotels_data|length, 'icon': '🏨', 'color': '#0066cc'},
                                        {'label': 'Requieren cancelación', 'value': hotels_data.values()|map(attribute='should_be_cancelled')|sum, 'icon': '⚠️', 'color': '#dc3545'},
                                        {'label': 'Confirmadas por link', 'value': hotels_data.values()|map(attribute='confirmed_by_link')|sum, 'icon': '✅', 'color': '#28a745'},
                                        {'label': 'Pendientes OK', 'value': hotels_data.values()|map(attribute='pending_reservations')|sum, 'icon': '⏳', 'color': '#ffc107'},
                                        {'label': 'Canceladas', 'value': hotels_data.values()|map(attribute='cancelled_yesterday')|sum, 'icon': '❌', 'color': '#6c757d'}
                                    ] %}
                                    
                                    {% for kpi in kpis %}
                                    <td style="text-align: center; padding: 0 8px; {% if not loop.last %}border-right: 1px solid #dee2e6;{% endif %}">
                                        <div style="font-size: 24px; margin-bottom: 8px;">{{ kpi.icon }}</div>
                                        <div style="font-size: 32px; font-weight: 700; color: {{ kpi.color }}; margin: 0;">{{ kpi.value }}</div>
                                        <div style="font-size: 12px; color: #6c757d; margin-top: 4px;">{{ kpi.label }}</div>
                                    </td>
                                    {% endfor %}
                                </tr>
                            </table>
                                    </td>
                                </tr>
                </table>

                <!-- Necesita gestión -->
                {% set hotels_with_issues = hotels_data.items()|selectattr('1.should_be_cancelled', 'gt', 0)|list %}
                {% if hotels_with_issues %}
                <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 32px;">
                    <tr>
                        <td style="background-color: #fff2f2; border: 2px solid #dc3545; border-radius: 8px; padding: 24px;">
                            <h2 style="font-size: 20px; font-weight: 700; color: #dc3545; margin: 0 0 16px 0;">⚡ Necesita gestión</h2>
                            <p style="color: #721c24; margin: 0 0 20px 0; font-size: 16px;">Los siguientes hoteles tienen reservas que deberían ser canceladas:</p>
                            
                            {% for hotel_code, hotel_data in hotels_with_issues %}
                            <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border: 1px solid #dc3545; border-radius: 6px; margin-bottom: 16px;">
                                <tr>
                                    <td style="padding: 16px;">
                                        <h3 style="font-size: 16px; font-weight: 600; color: #dc3545; margin: 0 0 12px 0;">🏨 {{ hotel_code|upper }}</h3>
                                        
                                                                                 {% for reservation in hotel_data.reservations %}
                                         {% if reservation.should_be_cancelled %}
                                         <div style="background-color: #fff5f5; border-left: 4px solid #dc3545; padding: 12px; margin: 8px 0; border-radius: 4px;">
                                             <div style="font-weight: 600; color: #1a1a1a; margin-bottom: 4px;">📋 {{ reservation.identifier }}</div>
                                             <div style="font-size: 14px; color: #6c757d; margin-bottom: 4px;">📅 {{ reservation.startDate }} - {{ reservation.endDate }}</div>
                                             <div style="font-size: 14px; color: #6c757d; margin-bottom: 4px;">🕐 Creada: {{ reservation.timestamp }}</div>
                                             <div style="font-size: 14px; color: #6c757d; margin-bottom: 4px;">💰 Total: €{{ "%.2f"|format(reservation.total_price|float) }} | Pagado: €{{ "%.2f"|format(reservation.total_paid|float) }}</div>
                                             <div style="font-size: 14px; color: #dc3545; font-weight: 600;">⚠️ {{ reservation.reason }}</div>
                                        </div>
                                         {% endif %}
                                         {% endfor %}
                                    </td>
                                </tr>
                            </table>
                {% endfor %}
                        </td>
                    </tr>
                                 </table>
                 {% endif %}

                 <!-- Reservas canceladas automáticamente ayer - Agrupadas por hotel -->
                 {% set hotels_with_cancelled_yesterday = [] %}
                 {% for hotel_code, hotel_data in hotels_data.items() %}
                     {% if hotel_data.cancelled_yesterday_reservations|length > 0 %}
                         {% set _ = hotels_with_cancelled_yesterday.append({
                             'hotel_code': hotel_code,
                             'hotel_name': hotel_data.hotel_name,
                             'reservations': hotel_data.cancelled_yesterday_reservations
                         }) %}
                     {% endif %}
                 {% endfor %}
                 
                 {% if hotels_with_cancelled_yesterday|length > 0 %}
                 <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 32px;">
                     <tr>
                         <td style="background-color: #fff8e1; border: 2px solid #ff9800; border-radius: 8px; padding: 24px;">
                             <h2 style="font-size: 20px; font-weight: 700; color: #e65100; margin: 0 0 16px 0;">📅 RESERVAS CANCELADAS AUTOMÁTICAMENTE AYER</h2>
                             <p style="color: #bf360c; margin: 0 0 20px 0; font-size: 16px;">
                                 Las siguientes reservas fueron canceladas automáticamente por el sistema en las últimas 24 horas:
                             </p>
                             
                             {% for hotel_info in hotels_with_cancelled_yesterday %}
                             <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border: 1px solid #ff9800; border-radius: 6px; margin-bottom: 20px;">
                                 <tr>
                                     <td style="padding: 20px;">
                                         <h3 style="font-size: 18px; font-weight: 700; color: #e65100; margin: 0 0 16px 0; border-bottom: 2px solid #ff9800; padding-bottom: 8px;">
                                             🏨 {{ hotel_info.hotel_code|upper }}
                                             {% if hotel_info.hotel_name %}
                                                 <span style="font-weight: 500; color: #6c757d; font-size: 14px;">- {{ hotel_info.hotel_name }}</span>
                                             {% endif %}
                                         </h3>
                                         
                                         <div style="background-color: #fef9f3; border-radius: 6px; padding: 12px; margin-bottom: 16px;">
                                             <div style="font-weight: 600; color: #e65100; margin-bottom: 8px;">
                                                 📊 Total reservas canceladas ayer: {{ hotel_info.reservations|length }}
                                             </div>
                                         </div>
                                         
                                         {% for reservation in hotel_info.reservations %}
                                         <div style="background-color: #fefbf3; border-left: 4px solid #ff9800; padding: 16px; margin: 12px 0; border-radius: 4px; border: 1px solid #ffe0b2;">
                                             <table width="100%" cellpadding="0" cellspacing="0">
                                                 <tr>
                                                     <td style="width: 60%;">
                                                         <div style="font-weight: 700; color: #1a1a1a; margin-bottom: 8px; font-size: 16px;">
                                                             📋 {{ reservation.identifier }}
                                                         </div>
                                                         <div style="font-size: 14px; color: #6c757d; margin-bottom: 4px;">
                                                             📅 <strong>Estancia:</strong> {{ reservation.startDate }} - {{ reservation.endDate }}
                                                         </div>
                                                         <div style="font-size: 14px; color: #6c757d; margin-bottom: 4px;">
                                                             🕐 <strong>Creada:</strong> {{ reservation.timestamp }}
                                                         </div>
                                                         {% if reservation.cancellationTimestamp %}
                                                         <div style="font-size: 14px; color: #d84315; margin-bottom: 4px; font-weight: 600;">
                                                             ❌ <strong>Cancelada:</strong> {{ reservation.cancellationTimestamp }}
                                                         </div>
                                                         {% endif %}
                                                     </td>
                                                     <td style="width: 40%; text-align: right; vertical-align: top;">
                                                         <div style="background-color: #ffffff; border: 1px solid #ff9800; border-radius: 4px; padding: 12px;">
                                                             <div style="font-size: 14px; color: #6c757d; margin-bottom: 4px;">
                                                                 💰 <strong>Total:</strong> €{{ "%.2f"|format(reservation.total_price|float) }}
                                                             </div>
                                                             <div style="font-size: 14px; color: #6c757d; margin-bottom: 8px;">
                                                                 💳 <strong>Pagado:</strong> €{{ "%.2f"|format(reservation.total_paid|float) }}
                                                             </div>
                                                             <div style="font-size: 12px; color: #e65100; font-weight: 600; text-align: center; background-color: #fff3e0; padding: 4px 8px; border-radius: 3px;">
                                                                 🤖 CANCELACIÓN AUTOMÁTICA
                                                             </div>
                                                         </div>
                                                     </td>
                                                 </tr>
                                             </table>
                                             
                                             {% if reservation.reason %}
                                             <div style="margin-top: 12px; padding: 8px; background-color: #fff3e0; border-radius: 4px; border-left: 3px solid #ff9800;">
                                                 <div style="font-size: 13px; color: #e65100; font-weight: 600;">
                                                     ℹ️ <strong>Motivo:</strong> {{ reservation.reason }}
                                                 </div>
                                             </div>
                                             {% endif %}
                                         </div>
                                         {% endfor %}
                                     </td>
                                 </tr>
                             </table>
                             {% endfor %}
                         </td>
                     </tr>
                 </table>
                 {% endif %}

                <!-- Reservas canceladas automáticamente -->
                {% set automatic_cancelled_reservations = [] %}
                {% for hotel_code, hotel_data in hotels_data.items() %}
                    {% for reservation in hotel_data.automatic_cancelled_reservations %}
                        {% set _ = automatic_cancelled_reservations.append({
                            'hotel': hotel_code,
                            'reservation': reservation
                        }) %}
                    {% endfor %}
                {% endfor %}
                
                {% if automatic_cancelled_reservations %}
                <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 32px;">
                    <tr>
                        <td style="background-color: #fff3e0; border: 2px solid #ff5722; border-radius: 8px; padding: 24px;">
                            <h2 style="font-size: 20px; font-weight: 700; color: #d84315; margin: 0 0 16px 0;">🤖 RESERVAS CANCELADAS AUTOMÁTICAMENTE</h2>
                            <p style="color: #bf360c; margin: 0 0 20px 0; font-size: 16px;">Las siguientes reservas fueron canceladas automáticamente por el sistema:</p>
                            
                            {% for item in automatic_cancelled_reservations %}
                            <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border: 1px solid #ff5722; border-radius: 6px; margin-bottom: 16px;">
                                <tr>
                                    <td style="padding: 16px;">
                                        <h3 style="font-size: 16px; font-weight: 600; color: #d84315; margin: 0 0 12px 0;">🏨 {{ item.hotel|upper }}</h3>
                                        
                                        <div style="background-color: #fef7f0; border-left: 4px solid #ff5722; padding: 12px; margin: 8px 0; border-radius: 4px;">
                                            <div style="font-weight: 600; color: #1a1a1a; margin-bottom: 4px;">📋 {{ item.reservation.identifier }}</div>
                                            <div style="font-size: 14px; color: #6c757d; margin-bottom: 4px;">📅 {{ item.reservation.startDate }} - {{ item.reservation.endDate }}</div>
                                            <div style="font-size: 14px; color: #6c757d; margin-bottom: 4px;">🕐 Creada: {{ item.reservation.timestamp }}</div>
                                            {% if item.reservation.cancellationTimestamp %}
                                            <div style="font-size: 14px; color: #6c757d; margin-bottom: 4px;">❌ Cancelada: {{ item.reservation.cancellationTimestamp }}</div>
                                            {% endif %}
                                            <div style="font-size: 14px; color: #6c757d; margin-bottom: 4px;">💰 Total: €{{ "%.2f"|format(item.reservation.total_price|float) }} | Pagado: €{{ "%.2f"|format(item.reservation.total_paid|float) }}</div>
                                            <div style="font-size: 14px; color: #d84315; font-weight: 600;">🤖 {{ item.reservation.reason }}</div>
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            {% endfor %}
                        </td>
                    </tr>
                </table>
                {% endif %}

                 <!-- Reservas canceladas (otras) -->
                {% set has_other_cancelled = [] %}
                {% for hotel_code, hotel_data in hotels_data.items() %}
                    {% if hotel_data.reservations %}
                        {% set _ = has_other_cancelled.append(1) %}
                    {% endif %}
                {% endfor %}
                
                {% if has_other_cancelled|length > 0 %}
                <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 32px;">
                    <tr>
                        <td style="background-color: #f8f9fa; border-radius: 8px; padding: 24px;">
                            <h2 style="font-size: 18px; font-weight: 600; color: #1a1a1a; margin: 0 0 20px 0;">❌ Otras reservas canceladas</h2>
                            <p style="color: #6c757d; margin: 0 0 16px 0; font-size: 14px;">Reservas canceladas que no fueron procesadas automáticamente:</p>
                            
                            {% for hotel_code, hotel_data in hotels_data.items() %}
                            {% if hotel_data.reservations %}
                            <table width="100%" cellpadding="0" cellspacing="0" style="background-color: #ffffff; border: 1px solid #dee2e6; border-radius: 6px; margin-bottom: 16px;">
                                <tr>
                                    <td style="padding: 16px;">
                                        <h3 style="font-size: 16px; font-weight: 600; color: #1a1a1a; margin: 0 0 12px 0;">🏨 {{ hotel_code|upper }}</h3>
                                        <p style="color: #6c757d; margin: 0 0 12px 0; font-size: 14px;">Total reservas en reporte: {{ hotel_data.reservations|length }}</p>
                                        
                                        <div style="background-color: #f8f9fa; border-radius: 4px; padding: 12px; margin: 8px 0;">
                                            <div style="font-weight: 600; color: #1a1a1a; margin-bottom: 8px;">📋 Identificadores:</div>
                                            {% for reservation in hotel_data.reservations %}
                                            <div style="display: inline-block; background-color: #e9ecef; color: #495057; padding: 4px 8px; margin: 2px; border-radius: 4px; font-size: 12px; font-family: monospace;">
                                                {{ reservation.identifier }}
                                                {% if reservation.should_be_cancelled %}
                                                <span style="color: #dc3545; font-weight: 600;"> ⚠️</span>
                                                {% elif reservation.cancelled_yesterday %}
                                                <span style="color: #ff9800; font-weight: 600;"> 📅</span>
                                                {% endif %}
                                            </div>
                                            {% endfor %}
                                        </div>
                                    </td>
                                </tr>
                            </table>
                            {% endif %}
                            {% endfor %}
                        </td>
                    </tr>
                </table>
                {% endif %}

                 <!-- Resumen por hotel -->
                <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 32px;">
                    <tr>
                        <td style="background-color: #f8f9fa; border-radius: 8px; padding: 24px;">
                            <h2 style="font-size: 18px; font-weight: 600; color: #1a1a1a; margin: 0 0 20px 0;">🏨 Detalle por Hotel</h2>
                            
                            <table width="100%" cellpadding="0" cellspacing="0" style="border-collapse: collapse; border: 1px solid #dee2e6; border-radius: 6px; overflow: hidden;">
                                <tr style="background-color: #e9ecef;">
                                    <td style="padding: 12px; font-weight: 600; font-size: 14px; border-bottom: 1px solid #dee2e6;">Hotel</td>
                                    <td style="padding: 12px; font-weight: 600; font-size: 14px; border-bottom: 1px solid #dee2e6; text-align: center;">Total</td>
                                    <td style="padding: 12px; font-weight: 600; font-size: 14px; border-bottom: 1px solid #dee2e6; text-align: center;">⚠️ Cancelar</td>
                                    <td style="padding: 12px; font-weight: 600; font-size: 14px; border-bottom: 1px solid #dee2e6; text-align: center;">⏳ Pendientes</td>
                                    <td style="padding: 12px; font-weight: 600; font-size: 14px; border-bottom: 1px solid #dee2e6; text-align: center;">❌ Canceladas</td>
                        </tr>
                                {% for hotel_code, hotel_data in hotels_data.items() %}
                                <tr style="{% if hotel_data.should_be_cancelled > 0 %}background-color: #fff2f2;{% else %}background-color: #ffffff;{% endif %}">
                                    <td style="padding: 12px; font-size: 14px; border-bottom: 1px solid #dee2e6; font-weight: 500;">{{ hotel_code }}</td>
                                    <td style="padding: 12px; font-size: 14px; border-bottom: 1px solid #dee2e6; text-align: center;">{{ hotel_data.total_reservations }}</td>
                                    <td style="padding: 12px; font-size: 14px; border-bottom: 1px solid #dee2e6; text-align: center; {% if hotel_data.should_be_cancelled > 0 %}color: #dc3545; font-weight: 600;{% endif %}">{{ hotel_data.should_be_cancelled }}</td>
                                    <td style="padding: 12px; font-size: 14px; border-bottom: 1px solid #dee2e6; text-align: center; color: #ffc107;">{{ hotel_data.pending_reservations }}</td>
                                    <td style="padding: 12px; font-size: 14px; border-bottom: 1px solid #dee2e6; text-align: center; color: #6c757d;">{{ hotel_data.cancelled_yesterday }}</td>
                        </tr>
                    {% endfor %}
                            </table>
                        </td>
                    </tr>
                </table>

                <!-- Footer -->
                <table width="100%" cellpadding="0" cellspacing="0">
                    <tr>
                        <td style="text-align: center; padding-top: 24px; border-top: 1px solid #dee2e6;">
                            <p style="color: #6c757d; font-size: 12px; margin: 0;">
                                💡 <strong>Paraty Tech - Sistema de Monitoreo Automático</strong><br>
                                Este reporte se genera automáticamente cada día para ayudarte a gestionar las reservas pendientes.<br>
                                {% if simulation_info %}
                                <span style="color: #dc3545; font-weight: 600;">🧪 MODO SIMULACIÓN - Este email normalmente se enviaría a: {{ simulation_info }}</span>
        {% endif %}
                            </p>
                        </td>
                    </tr>
                </table>

            </td>
        </tr>
    </table>

</body>
</html> 