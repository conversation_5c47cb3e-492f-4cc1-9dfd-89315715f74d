# Note that we are trusting Hotel-webs!
# We expect hotel-webs to maintain the cache timestamps up to date
import logging
import pickle

import redis

from paraty_commons_3.datastore.datastore_utils import get_location_prefix
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
import os

REDIS_PASSWORD = "nnoHL13EX/pf9uA0+glQnVTzn6ZC/brfzh5gifpedn2TYtR0SrNAmKgh1PZVnrYuaHTbKtk7sjEa8KHJ"
HOTEL_WEBS_EUROPE_REDIS = "34.78.221.41"  # See https://console.cloud.google.com/compute/instancesDetail/zones/europe-west1-b/instances/hotel-webs-eu-random?project=admin-hotel3
HOTEL_WEBS_AMERICA_REDIS = "34.67.168.188"  # https://console.cloud.google.com/compute/instancesDetail/zones/us-central1-a/instances/hotel-webs-usa-random?project=admin-hotel3
REDIS_PORT = 6666

# We use a different redis for persistence
PERSISTENCE_EUROPE_REDIS = '************'  # See https://console.cloud.google.com/compute/instancesDetail/zones/europe-west1-b/instances/redis-common-projects-non-critical?project=admin-hotel3
PERSISTENCE_AMERICA_REDIS = '***********'  # See https://console.cloud.google.com/compute/instancesDetail/zones/us-central1-a/instances/redis--usa--common-projects-non-critical?project=admin-hotel3

# If the project is in the US, we use the US redis so define the environment variable
PERSISTENCE_REDIS_HOST = os.environ.get('MANAGERS_CACHE_PERSISTENCE_REDIS') or PERSISTENCE_EUROPE_REDIS

# In case we want to change the frequency of the timestamp checks (in seconds)
TIMESTAMP_CHECK_INTERVAL = int(os.environ.get('TIMESTAMP_CHECK_INTERVAL') or 300)

logging.info(f"Timestamp checking internal: {TIMESTAMP_CHECK_INTERVAL}")

POOL_REDIS = {}

def _get_entity_timestamps_host(hotel_code: str) -> str:
    location = get_location_prefix(hotel_code)
    if location and location.startswith('s'):
        return HOTEL_WEBS_AMERICA_REDIS

    return HOTEL_WEBS_EUROPE_REDIS

def _get_persistence_host(hotel_code: str) -> str:
    location = get_location_prefix(hotel_code)
    if location and location.startswith('s'):
        return PERSISTENCE_AMERICA_REDIS

    return PERSISTENCE_EUROPE_REDIS


def _get_entities_connection(hotel_code: str) -> redis.Redis:

    host = _get_entity_timestamps_host(hotel_code)

    if not POOL_REDIS.get(host):
        logging.info("Creating redis connection to obtain timestamps for hotel_code %s: %s" % (hotel_code, host))
        POOL_REDIS[host] = redis.ConnectionPool(host=host, port=REDIS_PORT, password=REDIS_PASSWORD, socket_timeout=3, socket_connect_timeout=3)

    connection = redis.Redis(connection_pool=POOL_REDIS[host])
    return connection

def _get_persistence_connection(hotel_code: str) -> redis.Redis:

    # We want this to be as close as possible from the place in which it is running
    host = PERSISTENCE_REDIS_HOST

    if not POOL_REDIS.get(host):
        logging.info("Creating redis connection pool for persistence %s" % host)
        POOL_REDIS[host] = redis.ConnectionPool(host=host, port=REDIS_PORT, password=REDIS_PASSWORD, socket_timeout=3, socket_connect_timeout=3)

    connection = redis.Redis(connection_pool=POOL_REDIS[host])
    return connection


def _generate_timestamp_key(tag, hotel_code):
    return 'cache_webs_%s_%s' % (tag, hotel_code)


@timed_cache(seconds=TIMESTAMP_CHECK_INTERVAL)
def get_all_entities_timestamps(hotel_code) -> dict[str, str |None]:
    target_key = _generate_timestamp_key('all_entities', hotel_code)
    logging.info(f"Getting all entities timestamps {target_key}")
    results = _get_entities_connection(hotel_code).get(target_key)
    if results:
        results = pickle.loads(results)
        for key_element, key_value in results.items():
            if type(key_value) is bytes:
                results[key_element] = key_value.decode('utf-8')

        return results
    # Note that we are relying on the fact that the cache is updated by hotel-webs, so we never update this timestamps


def get_entry_from_persistence(hotel_code: str, entry_key: str) -> str | None:
    result = _get_persistence_connection(hotel_code).get(entry_key)
    return result

def set_entry_in_persistence(hotel_code: str, entry_key: str, entry_value: str | bytes, ttl: int) -> None:
    _get_persistence_connection(hotel_code).setex(entry_key, ttl, entry_value)


if __name__ == '__main__':
    set_entry_in_persistence('test-hotel', 'test-key', 'test-value', 60)
    print(get_entry_from_persistence('test-hotel', 'test-key'))