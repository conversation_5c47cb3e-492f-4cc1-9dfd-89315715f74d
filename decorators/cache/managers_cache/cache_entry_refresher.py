import logging
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor
from typing import Callable

import requests
from flask import g
from paraty import Config
from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.decorators.cache.managers_cache.redis_utils import get_all_entities_timestamps
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_internal_url

MAX_BACKGROUND_EXECUTOR_THREADS = 2

thread_pool_executor = ThreadPoolExecutor(MAX_BACKGROUND_EXECUTOR_THREADS)
executing_currently = {}


def execute_in_background(hotel_code: str, entry_key: str, f: Callable, *args, **kwargs):
    # No point in doing the same thing twice

    # logging.info("Executing currently %s" % len(executing_currently))

    if entry_key in executing_currently:
        logging.debug(f"Ignoring {entry_key}, already executing")
        return

    executing_currently[entry_key] = True  # TODO, change by timestamp
    thread_pool_executor.submit(_execute_and_inform, hotel_code, entry_key, f, *args, **kwargs)


def _execute_and_inform(hotel_code: str, entry_key: str, f, *args, **kwargs):
    try:
        logging.info(f"Executing in background: {entry_key}")
        from paraty import app
        with app.app_context():
            if hasattr(g, 'requests_cache'):
                g.requests_cache = {}

            # We need to make sure we do not enter into a loop (i.e. if a nested call also has cache)
            # Also need to make sure we do not cache not fresh data
            g.disabled_background_refresh = True

            f(hotel_code, *args, **kwargs)


    except Exception as e:
        logging.error(f"Error executing: {entry_key}")
        logging.error(e)
        logging.error(make_traceback())
    finally:
        executing_currently.pop(entry_key, None)
        logging.info(f"Finished executing: {entry_key}")


def refresh_entity_timestamps(entity: str, hotel_code: str, source: str = Config.PROJECT) -> bool:
    """
    Refreshes the timestamp of the entity in the cache.
    For now, it calls the hotel to refresh the cache.
    :param entity: The entity to refresh
    :param hotel_code: The hotel code
    :param source: The source. I.e. who is refreshing the cache
    :return: True if successful, False otherwise
    """
    hotel_url = get_internal_url(hotel_code)
    target_url = hotel_url + f'/flush_entity?entity={entity}&namespace={hotel_code}&source={source}'
    logging.info(f'Refreshing timestamps for {entity} in {hotel_code}')
    response = requests.get(target_url)
    if response.status_code == 200:
        get_all_entities_timestamps(hotel_code, _force_refresh=True) # Refresh all timestamps in memory cache
        return True
    else:
        logging.error(f"Error refreshing timestamps for {entity} in {hotel_code}")
        logging.error(f'Status code: {response.status_code}')
        logging.error(response.text)
        return False
