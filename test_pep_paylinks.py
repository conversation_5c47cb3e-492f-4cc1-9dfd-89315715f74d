#!/usr/bin/env python3
"""
Script de prueba para las funciones de pep_paylinks_summary.py
Ejecutar desde la raíz del proyecto: python test_pep_paylinks.py
"""

import sys
import os

# Agregar el directorio src al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, 'src')
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Configurar logging básico
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def main():
    """Función principal para probar las funciones de pep_paylinks"""
    try:
        print("🔍 Probando funciones de pep_paylinks_summary.py...")
        
        # Importar las funciones
        from paraty.development.pep_paylinks_summary import (
            get_hotels_with_pep_paylinks,
            get_hotels_emails_by_pep_paylinks_config,
            get_hotels_account_managers_by_pep_paylinks,
            get_hotels_all_emails_by_pep_paylinks,
            get_hotels_with_pep_paylinks_summary
        )
        
        print("\n✅ Imports exitosos!")
        
        # Probar función básica
        print("\n📊 Obteniendo hoteles con pep_paylinks...")
        hotels = get_hotels_with_pep_paylinks()
        print(f"✅ Encontrados {len(hotels)} hoteles con pep_paylinks")
        
        # Mostrar algunos hoteles de ejemplo
        if hotels:
            print("\n🏨 Primeros 5 hoteles encontrados:")
            for i, hotel in enumerate(hotels[:5]):
                print(f"  {i+1}. {hotel.get('name', 'N/A')} ({hotel.get('applicationId', 'N/A')})")
        
        # Probar función de emails de configuración
        print("\n📧 Obteniendo emails de configuración...")
        config_emails = get_hotels_emails_by_pep_paylinks_config()
        print(f"✅ Encontrados {len(config_emails)} emails de configuración")
        
        # Probar función de account managers
        print("\n👤 Obteniendo account managers...")
        account_managers = get_hotels_account_managers_by_pep_paylinks()
        print(f"✅ Encontrados {len(account_managers)} account managers")
        
        # Probar función combinada
        print("\n🔗 Obteniendo todos los emails combinados...")
        all_emails = get_hotels_all_emails_by_pep_paylinks()
        print(f"✅ Encontrados {len(all_emails)} emails únicos totales")
        
        # Mostrar resumen
        print("\n📋 Resumen final:")
        summary = get_hotels_with_pep_paylinks_summary()
        print(f"  - Total hoteles con pep_paylinks: {summary['total_hotels']}")
        print(f"  - Emails de configuración: {len(config_emails)}")
        print(f"  - Account managers: {len(account_managers)}")
        print(f"  - Emails únicos totales: {len(all_emails)}")
        
        print("\n🎉 ¡Todas las funciones funcionan correctamente!")
        
    except ImportError as e:
        print(f"❌ Error de importación: {e}")
        print("💡 Asegúrate de ejecutar este script desde la raíz del proyecto")
        return 1
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 