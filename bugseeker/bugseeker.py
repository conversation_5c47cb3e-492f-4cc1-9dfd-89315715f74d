import io
import json
import logging
import traceback
import requests

try:
    from google.auth import default
except:
    logging.info("you must add google-auth to requirements")

BUG_SEEKER_SERVICE = 'https://europe-west1-assistant-seeker.cloudfunctions.net/notify_kpi'
BUG_SEEKER_SERVICE_HEADER = {"content-type": "application/json"}
BUG_SEEKER_SERVICE_TIMEOUT = 3


def _send_notification_to_bug_seeker(code, message, project_id, subject='', hotel_code='', departments=[]):
    try:
        data_to_send = {
            'code': '',
            'hotel_code': hotel_code,
            'application_id': project_id,
            'departments': departments,
            'metadata': {
                'title': code,
                'subject': subject if subject else code,
                'content': message
            }
        }
        response = requests.post(BUG_SEEKER_SERVICE, headers=BUG_SEEKER_SERVICE_HEADER, data=json.dumps(data_to_send), timeout=BUG_SEEKER_SERVICE_TIMEOUT)
        response.raise_for_status()

    except Exception as e:
        logging.error(e)


def send_notification_to_bug_seeker(code, message, subject='', hotel_code='', departments=[]):
    credentials, project_id = default()
    _send_notification_to_bug_seeker(code, message, project_id, subject=subject, hotel_code=hotel_code, departments=departments)


def send_exception_notification_to_bug_seeker(code, exception, subject='', hotel_code='', departments=[]):
    credentials, project_id = default()
    output_buffer = io.StringIO()
    traceback.print_exception(type(exception), exception, exception.__traceback__, file=output_buffer)
    message = output_buffer.getvalue()

    _send_notification_to_bug_seeker(code, message, project_id, subject=subject, hotel_code=hotel_code, departments=departments)
