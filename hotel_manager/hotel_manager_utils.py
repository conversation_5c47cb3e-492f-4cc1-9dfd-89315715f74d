import io
import json
import zipfile
from functools import lru_cache
import requests

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache

# Time we want the cache to store this data
from paraty_commons_3.decorators.retry import retry

import logging

from paraty_commons_3.decorators.timerdecorator import timeit

CACHE_HOURS = 24

MANAGER_URL = 'https://admin-hotel.appspot.com'

# fmatheis, Note that we are not using Secret Manager here because this is not sensible information
AUTHENTICATION = 'Basic ZGV2OnJ1bGVz'

local_cache = {}


def flush_entity_cache(hotel_code, entity_name):
    flush_url = '%s/integrations?action=flush_entity&hotel_code=%s&entity_name=%s' % (MANAGER_URL, hotel_code, entity_name)
    requests.get(flush_url, timeout=20)


@timed_cache(hours=CACHE_HOURS)
@timeit
def get_hotel_project_and_namespace(hotel_code):
    current_hotel = get_hotel_by_application_id(hotel_code)

    if not current_hotel:
        return None, None

    return _get_project_id_and_namespace(current_hotel)


@timed_cache(hours=CACHE_HOURS)
def get_hotel_by_application_id(hotel_id):
    return get_all_hotels().get(hotel_id)


@lru_cache(maxsize=1024)
def get_hotel_by_numeric_id(numeric_id):
    all_hotels = get_all_hotels()
    hotel = [each_hotel for key, each_hotel in all_hotels.items() if int(each_hotel.get('id')) == int(numeric_id)]

    return hotel[0] if len(hotel) > 0 else None


def _get_local_project_name():
    actual_project = ''
    try:
        from paraty.config import Config
        actual_project = Config.PROJECT
    except Exception:
        pass
    return actual_project


@retry(Exception, tries=3, delay=3, backoff=2)
def get_all_hotel_metadata(max_age=0):

    result = requests.get(
        f"https://europe-west1-build-tools-2.cloudfunctions.net/get_all_from_entity2?hotel_code=admin-hotel&entity_name=HotelMetadata&max_age={max_age}&referrer={_get_local_project_name()}",
        headers={'Authorization': AUTHENTICATION}, timeout=20)

    if result.status_code == 200:
        content = result.content
        if result.headers.get('Content-Type') == 'application/zip':
            content = zipfile.ZipFile(io.BytesIO(result.content)).open('HotelMetadata.json').read()

        full_hotel_list = json.loads(content)
        result = {x['applicationId']: x for x in full_hotel_list}

        return result
    else:
        logging.error(f"Problems retrieving HotelMetadata from cloudfunctions: {result.status_code}")
        raise Exception("Problems retrieving HotelMetadata from cloudfunctions")


@lru_cache(maxsize=1024)
def get_hotel_metadata_by_application_id(hotel_id):
    # First try with cached data, 1 day
    all_metadata = get_all_hotel_metadata(86400)
    if hotel_id in all_metadata:
        return all_metadata.get(hotel_id)

    return get_all_hotel_metadata(0).get(hotel_id)

def get_hotel_metadata_by_application_id_without_cache(hotel_id):
    return get_all_hotel_metadata().get(hotel_id)


@timed_cache(hours=CACHE_HOURS)
@retry(Exception, tries=3, delay=3)
def get_all_users():



    result = requests.get(
        f"https://europe-west1-build-tools-2.cloudfunctions.net/get_all_from_entity2?hotel_code=admin-hotel&entity_name=ParatyUser&max_age=3600&referrer={_get_local_project_name()}",
        headers={'Authorization': AUTHENTICATION}, timeout=20)

    content = result.content
    if result.headers.get('Content-Type') == 'application/zip':
        content = zipfile.ZipFile(io.BytesIO(result.content)).open('ParatyUser.json').read()

    all_users = json.loads(content)
    return all_users


@timed_cache(hours=CACHE_HOURS)
@retry(Exception, tries=3, delay=3)
def get_all_hotels():
    target_host = 'https://europe-west1-build-tools-2.cloudfunctions.net'
    target_function = 'get_all_from_entity2'
    target_function_fallback = 'get_all_from_entity'

    target_params = f'hotel_code=admin-hotel&entity_name=HotelApplication&max_age=300&referrer={_get_local_project_name()}'

    request_failed = False
    result = None
    try:
        result = requests.get(
            f"{target_host}/{target_function}?{target_params}",
            headers={'Authorization': AUTHENTICATION}, timeout=20)
        content = result.content
        if result.status_code != 200:
            raise Exception(f"Problems retrieving HotelApplication from cloudfunctions: {result.status_code}")
        if result.headers.get('Content-Type') == 'application/zip':
            content = zipfile.ZipFile(io.BytesIO(result.content)).open('HotelApplication.json').read()
    except Exception as e:
        logging.error(e)
        from paraty.development.development_utils import get_latest_file
        from_storage = get_latest_file('admin-hotel', 'HotelApplication')
        content = from_storage.download_as_bytes()
        content = zipfile.ZipFile(io.BytesIO(content)).open('HotelApplication.json').read()

    full_hotel_list = json.loads(content)
    result = {x['applicationId']: x for x in full_hotel_list}

    return result


def get_all_hotels_without_cache():
    result = requests.get(
        f"https://europe-west1-build-tools-2.cloudfunctions.net/get_all_from_entity2?hotel_code=admin-hotel&entity_name=HotelApplication&max_age=0&referrer={_get_local_project_name()}",
        headers={'Authorization': AUTHENTICATION}, timeout=20)

    content = result.content
    if result.headers.get('Content-Type') == 'application/zip':
        content = zipfile.ZipFile(io.BytesIO(result.content)).open('HotelApplication.json').read()

    full_hotel_list = json.loads(content)
    result = {x['applicationId']: x for x in full_hotel_list}

    return result


def get_all_valid_hotels():
    '''
	Returns all the Hotels that are enabled and in production
	'''
    hotels = get_all_hotels()
    return [x for x in list(hotels.values()) if x.get('enabled') and x.get('inProduction')]


def get_exit_name_hotels(name, without_cache=None):
    if without_cache:
        hotels = get_all_hotels_without_cache()
    else:
        hotels = get_all_hotels()
    return [x for x in list(hotels.values()) if x.get('name').upper() == name.upper()]


def get_exit_hotelCode(hotel_code, without_cache=None):
    if without_cache:
        hotels = get_all_hotels_without_cache()
    else:
        hotels = get_all_hotels()
    return [x for x in list(hotels.values()) if x.get('applicationId').upper() == hotel_code.upper()]


def _get_project_id_and_namespace(hotel):
    namespace = None
    project_id = hotel['applicationId']
    if 'multi' in hotel['url']:
        project_id = hotel['url'][17:]
        project_id = project_id[:project_id.find('.appspot')]
        namespace = hotel['applicationId']
    return project_id, namespace


def get_location_profix_of_applicationId(application_id):
    all_hotels = get_all_hotels()
    all_valid_hotels = [x for x in list(all_hotels.values()) if x.get('enabled')]

    entities = []
    hotel_code = ""
    for x in all_valid_hotels:
        application = x.get("url").replace("https://rest-dot-", "").replace(".appspot.com", "")
        ind = application.find("/multi/")
        if ind > 1:
            nuevo = True
            application = application[0:ind]
            if application == application_id:
                hotel_code = x.get("applicationId")
                break

    entities = datastore_communicator.get_using_entity_and_params('HotelMetadata',
                                                                  search_params=[('applicationId', '=', hotel_code)],
                                                                  hotel_code='admin-hotel')

    result = ""
    for item in entities:
        result = item.get("location_prefix")

    return result


def get_internal_url(hotel_code):
    if not hotel_code:
        return ''

    target_namespace = hotel_code.replace('r__', '')
    actual_appid, actual_namespace = get_hotel_project_and_namespace(target_namespace)

    if actual_namespace:
        url_builded = "https://%s-dot-%s.appspot.com" % (actual_namespace, actual_appid)
    else:
        url_builded = "https://%s.appspot.com" % (actual_appid)

    return url_builded


@timed_cache(hours=3)
def _get_all_hotels_metadata():
    all_entities = datastore_communicator.get_using_entity_and_params('HotelMetadata', hotel_code='admin-hotel')
    return all_entities


@timed_cache(hours=3)
def get_all_hotels_by_application(application):
    all_hotels = get_all_hotels()
    all_valid_hotels = []
    for hotel_code in all_hotels.keys():
        rest_url = all_hotels[hotel_code].get('url', '')
        application_name = rest_url.split("-dot-")[-1].split(".appspot.com")[0]
        if application_name == application:
            all_valid_hotels.append(all_hotels[hotel_code])

    return all_valid_hotels

def get_account_manager(hotel_code):
    my_metadata = filter(lambda x: x['applicationId'] == hotel_code, _get_all_hotels_metadata())
    return next(my_metadata)['accountManager']


if __name__ == '__main__':
    print(get_hotel_metadata_by_application_id("hotel-puentereal"))
# print(get_hotel_by_application_id("hotel-puentereal"))
# print(get_all_hotels())
