#!/usr/bin/env python3
"""
Script para configurar el entorno y resolver dependencias
Ejecutar: python setup_environment.py
"""

import os
import sys
import subprocess
import venv
from pathlib import Path

def check_python_version():
    """Verificar que la versión de Python sea compatible"""
    if sys.version_info < (3, 7):
        print("❌ Se requiere Python 3.7 o superior")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detectado")
    return True

def create_virtual_environment():
    """Crear entorno virtual si no existe"""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("✅ Entorno virtual ya existe")
        return venv_path
    
    print("🔧 Creando entorno virtual...")
    try:
        venv.create("venv", with_pip=True)
        print("✅ Entorno virtual creado exitosamente")
        return venv_path
    except Exception as e:
        print(f"❌ Error creando entorno virtual: {e}")
        return None

def get_venv_python():
    """Obtener la ruta del Python del entorno virtual"""
    if os.name == 'nt':  # Windows
        return "venv/Scripts/python.exe"
    else:  # Unix/Linux/macOS
        return "venv/bin/python"

def install_requirements():
    """Instalar dependencias desde requirements.txt"""
    requirements_file = Path("src/requirements.txt")
    
    if not requirements_file.exists():
        print("⚠️  No se encontró requirements.txt")
        return False
    
    print("📦 Instalando dependencias...")
    try:
        venv_python = get_venv_python()
        subprocess.run([
            venv_python, "-m", "pip", "install", "-r", str(requirements_file)
        ], check=True)
        print("✅ Dependencias instaladas exitosamente")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error instalando dependencias: {e}")
        return False

def setup_pythonpath():
    """Configurar PYTHONPATH para el proyecto"""
    src_path = Path("src").absolute()
    
    # Crear script de activación
    if os.name == 'nt':  # Windows
        activate_script = "venv/Scripts/activate.bat"
        pythonpath_script = "venv/Scripts/set_pythonpath.bat"
        pythonpath_content = f"set PYTHONPATH={src_path};%PYTHONPATH%"
    else:  # Unix/Linux/macOS
        activate_script = "venv/bin/activate"
        pythonpath_script = "venv/bin/set_pythonpath.sh"
        pythonpath_content = f"export PYTHONPATH={src_path}:$PYTHONPATH"
    
    # Crear script para configurar PYTHONPATH
    with open(pythonpath_script, 'w') as f:
        f.write(pythonpath_content)
    
    # Hacer ejecutable en Unix
    if os.name != 'nt':
        os.chmod(pythonpath_script, 0o755)
    
    print(f"✅ PYTHONPATH configurado: {src_path}")
    return True

def main():
    """Función principal"""
    print("🚀 Configurando entorno para build-tools-2...")
    
    # Verificar Python
    if not check_python_version():
        return 1
    
    # Crear entorno virtual
    venv_path = create_virtual_environment()
    if not venv_path:
        return 1
    
    # Instalar dependencias
    if not install_requirements():
        print("⚠️  Continuando sin instalar dependencias...")
    
    # Configurar PYTHONPATH
    setup_pythonpath()
    
    print("\n✅ Entorno configurado exitosamente!")
    print("\n📋 Para usar el entorno:")
    
    if os.name == 'nt':  # Windows
        print("  venv\\Scripts\\activate")
        print("  venv\\Scripts\\set_pythonpath.bat")
    else:  # Unix/Linux/macOS
        print("  source venv/bin/activate")
        print("  source venv/bin/set_pythonpath.sh")
    
    print("\n🔍 Para probar las funciones:")
    print("  python test_pep_paylinks.py")
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 