#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from postmarker.core import PostmarkClient
import logging

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_simple_email():
    """Prueba muy simple de envío de email"""
    try:
        postmark = PostmarkClient(server_token='************************************')
        
        response = postmark.emails.send(
            From='Sistema Monitoreo <<EMAIL>>',
            To='<EMAIL>',
            Subject='TEST SIMPLE - ¿Recibes este email?',
            HtmlBody='<h1>Test Simple</h1><p>Si recibes este email, el sistema funciona correctamente.</p>',
            TextBody='Test Simple\n\nSi recibes este email, el sistema funciona correctamente.'
        )
        
        logger.info("✅ Email simple enviado")
        logger.info("Response: {}".format(response))
        return True
        
    except Exception as e:
        logger.error("❌ Error: {}".format(e))
        return False

if __name__ == "__main__":
    test_simple_email() 