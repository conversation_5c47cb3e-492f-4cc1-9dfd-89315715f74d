import datetime
import logging

import requests

from paraty_commons_3 import date_utils
from paraty_commons_3.common_data.common_data_provider import get_hotel_advance_config_item, get_hotel_advance_config_value
from paraty_commons_3.date_utils import get_timestamp
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.generic_feed.feed_constants import MAX_HOTELS_PER_CALL, SPECIAL_SERVERS, EUROPE_ENGINE
from paraty_commons_3.generic_feed.generic_feed_handler import FeedSearch


def build_search_string(hotel_codes, search: FeedSearch):

    adults_room_1 = search.adults_1 or 2
    kids_room_1 = search.kids_1 or 0
    adults_room_2 = search.adults_2 or 0
    kids_room_2 = search.kids_2 or 0
    adults_room_3 = search.adults_3 or 0
    kids_room_3 = search.kids_3 or 0
    babies_room_1 = search.babies_1 or 0
    babies_room_2 = search.babies_2 or 0
    babies_room_3 = search.babies_3 or 0
    promocode = ''
    country = search.country or 'es'
    start_date = date_utils.date_to_string(date_utils.string_to_date(search.start_date, "%Y-%m-%d"), "%d/%m/%Y")
    end_date = date_utils.date_to_string(date_utils.string_to_date(search.end_date, "%Y-%m-%d"), "%d/%m/%Y")

    request_string = '/search/?'
    request_string += 'applicationId=' + hotel_codes + "&"
    request_string += f"countryCode={country}&"
    request_string += "startDate=" + start_date + "&"
    request_string += "endDate=" + end_date + "&"
    request_string += f"numRooms={search.num_rooms}&"
    request_string += f"adultsRoom1={adults_room_1}&"
    request_string += f"adultsRoom2={adults_room_2}&"
    request_string += f"adultsRoom3={adults_room_3}&"
    request_string += f"babiesRoom1={babies_room_1}&"
    request_string += f"babiesRoom2={babies_room_2}&"
    request_string += f"babiesRoom3={ babies_room_3}&"
    request_string += f"childrenRoom1={kids_room_1}&"
    request_string += f"childrenRoom2={kids_room_2}&"
    request_string += f"childrenRoom3={kids_room_3}"

    if search.num_rooms > 3:
        adults_room_4 = search.adults_4 or 0
        kids_room_4 = search.kids_4 or 0
        babies_room_4 = search.babies_4 or 0
        request_string += f"&adultsRoom4={adults_room_4}"
        request_string += f"&childrenRoom4={kids_room_4}"
        request_string += f"&babiesRoom4={babies_room_4}"

    if search.num_rooms > 4:
        adults_room_5 = search.adults_5 or 0
        kids_room_5 = search.kids_5 or 0
        babies_room_5 = search.babies_5 or 0
        request_string += f"&adultsRoom5={adults_room_5}"
        request_string += f"&childrenRoom5={kids_room_5}"
        request_string += f"&babiesRoom5={babies_room_5}"

    if search.num_rooms > 5:
        adults_room_6 = search.adults_6 or 0
        kids_room_6 = search.kids_6 or 0
        babies_room_6 = search.babies_6 or 0
        request_string += f"&adultsRoom6={adults_room_6}"
        request_string += f"&childrenRoom6={kids_room_6}"
        request_string += f"&babiesRoom6={babies_room_6}"

    request_string += "&ignoreStats=True"

    request_string += "&device=%s" % search.device or "Web"

    request_string += "&source=%s" % search.source

    # Note that the promocode should be left the last as it might contain weird characters
    request_string += "&promocode=" + promocode

    if search.accept_incomplete_results:
        request_string += "&acceptIncompleteRates=true"
    
    if search.ignore_promocodes:
        request_string += "&ignore_promocodes=True"

    return request_string


def build_hotel_chunks(hotel_ids: list) -> list:

    active_hotels, inactive_hotels = _split_hotels_between_active_or_not(hotel_ids)

    # In case of problems we quarantine hotels for a few minutes
    quarantined_hotels = [x for x in hotel_ids if not _is_application_id_valid(x)]
    inactive_hotels.extend(quarantined_hotels)
    active_hotels = [x for x in active_hotels if not x in quarantined_hotels]

    application_chunks = chunks(active_hotels, MAX_HOTELS_PER_CALL)
    special_servers = build_special_servers_dict(active_hotels)
    application_chunks = _separate_by_servers(application_chunks, special_servers)

    return application_chunks


@timed_cache(hours=1, key_builder=lambda x: 'cached_search_%s' % x[0], max_size=50000)
def _do_call_with_cache(request_string):
    result = requests.get(request_string, headers={'Authorization': "Basic cGFjbzpwYWNv"}, timeout=20)
    return result.content.decode('utf8')


def _split_hotels_between_active_or_not(hotel_ids):
    active_hotels = []
    inactive_hotels = []

    # Until we fix the API we need to return prices for all, as we don't know which yes and which not
    return hotel_ids, []


async def get(session, url, headers):

    try:
        resp = await session.request('GET', url=url, headers=headers, timeout=25)
        data = await resp.json()
        return data
    except Exception as e:
        logging.error(f"Exception while doing get to: {url}")
        logging.error(e)
        return None


# async def perform_searchs_asynchronously(requests_to_perform):
#
#     async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(verify_ssl=False)) as session:
#         tasks = []
#         for current_request in requests_to_perform:
#             tasks.append(get(session=session, url=current_request, headers={"Authorization": "Basic cGFjbzpwYWNv"}))
#         results = await asyncio.gather(*tasks, return_exceptions=True)
#         return results


@timed_cache(minutes=3)
def _clear_quarantine():
    global quarantine
    quarantine = set()


def _is_application_id_valid(application_id):
    # In case we need to clear it
    _clear_quarantine()

    return not application_id in quarantine


def chunks(l, n):
    """Yield successive n-sized chunks from l."""
    for i in range(0, len(l), n):
        yield l[i:i + n]


def build_special_servers_dict(active_hotels):
    '''
    Note that not everybody works with admin-hotel, i.e. Fuerte-hoteles or Hotetec
    These hotels will be moved to separete chunks
    '''

    special_servers = SPECIAL_SERVERS

    for hotel in active_hotels:

        if hotel in special_servers:
            continue

        has_special_server_config = get_hotel_advance_config_value(hotel, "Manager as proxy")
        if has_special_server_config:
            has_special_server = has_special_server_config
            if has_special_server and (("prestige" in has_special_server) or ("innsist" in has_special_server)):
                special_servers[hotel] = has_special_server

        hotel_in_europe = get_hotel_advance_config_value(hotel, "Use alternative manager")
        if hotel_in_europe and ('admin-hotel3' in hotel_in_europe or 'europe' in hotel_in_europe):
            special_servers[hotel] = EUROPE_ENGINE
            continue

    return special_servers


# global quarantine by instance
engine_calls_counter = {}


def _process_engine_in_quarantine(engine, seconds_for_quarantine=60, max_calls=30):
    '''
    :param engine:
    :param seconds_for_quarantine:
    :param max_calls:
    :return: False(OK)-> We can call engine without problems! True(KO)-> DANGER! Engine is in quarantine

    HOW it works:
    We have already created a counter?
        NO -> First time :) -> create a counter and return False (OK)
    else,
        It's been more than 'seconds_for_quarantine' without calls? ( seconds_from_last_call > seconds_for_quarantine)
            YES ->
                NO PROBLEM!! reset counter and return False (OK)
            NO ->
                < 'max_calls' -> counter +1 and return OK
                > 'max_calls' -> oh oh , nothing to do with counter  and return True (KO(
    '''

    global engine_calls_counter

    date_format = "%Y-%m-%d %H:%M:%S"
    now_ts = get_timestamp(format=date_format)

    if not engine_calls_counter.get(engine):
        # First time!
        engine_calls_counter[engine] = {'counter': 1, 'from_timestamp': now_ts}
        # engine is not in qurentine
        return False
    else:

        call_counter = engine_calls_counter[engine]
        from_timestamp = call_counter.get("from_timestamp")
        num_calls_done = call_counter.get("counter")

        diff = datetime.datetime.strptime(now_ts, date_format) - datetime.datetime.strptime(from_timestamp, date_format)

        seconds_from_last_call = diff.total_seconds()
        if seconds_from_last_call > seconds_for_quarantine:
            # no problem! longtime without calls -> reset calls counter!
            engine_calls_counter.pop(engine, None)
            # engine is not in quarantine
            return False
        else:
            if num_calls_done < max_calls:
                # no problem!  not max calls done yet -> Update Counter!
                call_counter["counter"] = num_calls_done + 1
                # engine is not in quarantine
                return False
            else:
                # DANGER: #engine is in quarantine
                logging.warning("Engine in quarantine! %s. num_calls_done from %s: %s", engine, from_timestamp, num_calls_done)
                return True


def _separate_by_servers(application_chunks, special_servers):
    '''
    Note that not everybody works with admin-hotel, i.e. Fuerte-hoteles or Hotetec
    These hotels will be moved to separete chunks
    '''

    result = []
    for chunk in application_chunks:
        for hotel in chunk:
            if hotel in special_servers:
                result.append([hotel])
                chunk.remove(hotel)
        if chunk:
            result.append(chunk)

    return result