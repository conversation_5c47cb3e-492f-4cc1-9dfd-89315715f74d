from paraty_commons_3.date_utils import validate_date_format
from paraty_commons_3.language_utils import ISO_TO_LANGUAGE_CODES

# Keep this to 1 for now, better for caching
MAX_HOTELS_PER_CALL = 1


class FeedSearch:

    def __init__(self):
        self.language = 'es'
        self.currency = 'EUR'
        self.num_rooms = 1
        self.country = 'es'
        self.device = 'desktop'
        self.accept_incomplete_results = False
        self.extra_data = {}
        self.adults_2 = 0
        self.adults_3 = 0
        self.adults_4 = 0
        self.adults_5 = 0
        self.adults_6 = 0
        self.kids_2 = 0
        self.kids_3 = 0
        self.kids_4 = 0
        self.kids_5 = 0
        self.kids_6 = 0
        self.babies_1 = 0
        self.babies_2 = 0
        self.babies_3 = 0
        self.babies_4 = 0
        self.babies_5 = 0
        self.babies_6 = 0
        self.ignore_promocodes = ""

    hotels: list
    start_date: str  # Format: YYYY-MM-DD
    end_date: str  # Format: YYYY-MM-DD
    country: str  # ISO 3166-1 alpha-2
    num_rooms: int
    adults_1: int
    adults_2: int
    adults_3: int
    adults_4: int
    adults_5: int
    adults_6: int
    kids_1: int
    kids_2: int
    kids_3: int
    kids_4: int
    kids_5: int
    kids_6: int
    babies_1: int
    babies_2: int
    babies_3: int
    babies_4: int
    babies_5: int
    babies_6: int
    device: str
    source: str  # Source of the request, i.e. trivago
    currency: str
    names: bool  # If true, the result will include the names of the entities
    language: str  # Language in which it will return the information
    accept_incomplete_results: bool  # If true, the server will return the results it has even if some days are closed
    ignore_promocodes: str
    extra_data: {}  # Additional params we might want to read from the request for further use

    def validate(self):
        if not self.start_date or not self.end_date:
            raise ValueError('Start and end dates are mandatory')

        if not validate_date_format(self.start_date) or not validate_date_format(self.end_date):
            raise ValueError('Start and end dates must be in format YYYY-MM-DD')

        if not self.hotels:
            raise ValueError('Hotels are mandatory')

        if type(self.num_rooms) != int or 3 < self.num_rooms < 1:
            raise ValueError('Number of rooms must an integer between 1 and 3')

        if type(self.adults_1) != int or self.adults_1 < 1:
            raise ValueError('Number of adults must an int and be at least 1')

        if self.language not in ISO_TO_LANGUAGE_CODES:
            # If it is not one of the known languages we will default to English
            self.language = 'en'
            # raise ValueError('Language must be a valid ISO 639-1 code: %s' % self.language)

        if self.device not in ['mobile', 'desktop']:
            raise ValueError('Device must be either mobile or desktop')


class SearchResponse:
    search: FeedSearch
    results: dict
    status: int
    names: dict
    metadata: dict


FUERTE_ENGINE = 'https://fuerte-adapter.appspot.com'
PRESTIGE_ENGINE = 'https://prestige-adapter.appspot.com'
US_ENGINE = 'https://integrations-gcgpxi45sq-uc.a.run.app'
# EUROPE_ENGINE = 'https://integrations-dot-admin-hotel3.appspot.com'
EUROPE_ENGINE = 'https://integrations-4fpduq6apq-ew.a.run.app'
INNSIST_ENGINE = 'https://parkroyal-seeker-dot-innsist-adapter.uc.r.appspot.com'

SPECIAL_SERVERS = {
    'daguisa-pasdelacasa': PRESTIGE_ENGINE,
    'daguisa-tulip': PRESTIGE_ENGINE,
    'daguisa-goldenfenix': PRESTIGE_ENGINE,
    'daguisa-euroski': PRESTIGE_ENGINE,
    'daguisa-canillo': PRESTIGE_ENGINE,
    'daguisa-sanbernat': PRESTIGE_ENGINE,

    # For performance reasons...
    'hotel-rosamar': 'https://integrations-gcgpxi45sq-uc.a.run.app',
    'montiel': 'https://integrations-gcgpxi45sq-uc.a.run.app'
}


QUARANTINE_ENGINE_CONFIGURATIONS = {
    PRESTIGE_ENGINE: {"seconds_for_quarantine": 60, "max_calls": 30},
    INNSIST_ENGINE: {"seconds_for_quarantine": 60, "max_calls": 30}
}
