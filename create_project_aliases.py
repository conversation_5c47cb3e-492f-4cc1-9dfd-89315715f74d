#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Script para crear alias de proyectos.
Permite acceder a proyectos usando su nombre sin guiones.
Ejemplo: siteminder-adapter -> siteminder
"""

import os
import sys

def get_projects_directory():
    """Obtiene el directorio donde están los proyectos"""
    # Asumimos que los proyectos están en el directorio padre
    current_dir = os.getcwd()
    projects_dir = os.path.dirname(current_dir)
    return projects_dir

def get_all_projects():
    """Obtiene todos los directorios que son proyectos"""
    projects_dir = get_projects_directory()
    projects = []
    
    if not os.path.exists(projects_dir):
        print("ERROR: Directorio de proyectos no encontrado: {}".format(projects_dir))
        return projects
    
    try:
        for item in os.listdir(projects_dir):
            item_path = os.path.join(projects_dir, item)
            if os.path.isdir(item_path) and not item.startswith('.'):
                projects.append(item)
    except OSError:
        print("ERROR: No se puede leer el directorio de proyectos")
        return projects
    
    return sorted(projects)

def create_alias_name(project_name):
    """Crea un alias removiendo guiones y caracteres especiales"""
    # Remover guiones y convertir a minúsculas
    alias = project_name.replace('-', '').replace('_', '').lower()
    return alias

def create_aliases():
    """Crea los alias para todos los proyectos"""
    projects = get_all_projects()
    
    if not projects:
        print("ERROR: No se encontraron proyectos")
        return
    
    print("Encontrados {} proyectos:".format(len(projects)))
    
    # Crear el archivo de alias
    home_dir = os.path.expanduser("~")
    alias_file = os.path.join(home_dir, '.bash_aliases')
    if not os.path.exists(alias_file):
        alias_file = os.path.join(home_dir, '.zshrc')
    
    # Crear contenido de alias
    alias_content = []
    alias_content.append("# Aliases generados automáticamente para proyectos")
    alias_content.append("# Generado por create_project_aliases.py")
    alias_content.append("")
    
    projects_dir = get_projects_directory()
    
    for project in projects:
        alias = create_alias_name(project)
        alias_content.append("alias {}='cd {}/{}'".format(alias, projects_dir, project))
        print("  {} -> {}".format(project, alias))
    
    # Escribir al archivo
    try:
        with open(alias_file, 'a') as f:
            f.write('\n'.join(alias_content) + '\n')
        print("\nSUCCESS: Aliases agregados a {}".format(alias_file))
        print("INFO: Ejecuta 'source ~/.bash_aliases' o reinicia tu terminal para activar los alias")
        
    except Exception as e:
        print("ERROR: Error escribiendo alias: {}".format(e))

def create_script_file():
    """Crea un script que se puede ejecutar directamente"""
    projects = get_all_projects()
    
    if not projects:
        print("ERROR: No se encontraron proyectos")
        return
    
    projects_dir = get_projects_directory()
    
    # Crear script
    script_content = []
    script_content.append("#!/bin/bash")
    script_content.append("# Script para navegar a proyectos")
    script_content.append("# Uso: ./go_to_project.sh <nombre_proyecto>")
    script_content.append("")
    script_content.append('PROJECTS_DIR="{}"'.format(projects_dir))
    script_content.append("")
    script_content.append('case "$1" in')
    
    for project in projects:
        alias = create_alias_name(project)
        script_content.append('    "{}")'.format(alias))
        script_content.append('        cd "$PROJECTS_DIR/{}"'.format(project))
        script_content.append('        echo "Navegando a {}"'.format(project))
        script_content.append("        ;;")
    
    script_content.append("    *)")
    script_content.append('        echo "Proyectos disponibles:"')
    for project in projects:
        alias = create_alias_name(project)
        script_content.append('        echo "  {} -> {}"'.format(alias, project))
    script_content.append("        ;;")
    script_content.append("esac")
    
    # Escribir script
    script_file = os.path.join(os.getcwd(), 'go_to_project.sh')
    try:
        with open(script_file, 'w') as f:
            f.write('\n'.join(script_content))
        
        # Hacer ejecutable
        os.chmod(script_file, 0755)
        
        print("\nSUCCESS: Script creado: {}".format(script_file))
        print("INFO: Uso: ./go_to_project.sh <nombre_proyecto>")
        print("INFO: Ejemplo: ./go_to_project.sh siteminder")
        
    except Exception as e:
        print("ERROR: Error creando script: {}".format(e))

def show_available_projects():
    """Muestra todos los proyectos disponibles"""
    projects = get_all_projects()
    
    if not projects:
        print("ERROR: No se encontraron proyectos")
        return
    
    print("Proyectos disponibles ({}):".format(len(projects)))
    print("-" * 50)
    
    for project in projects:
        alias = create_alias_name(project)
        print("  {:<20} -> {}".format(alias, project))

def main():
    """Función principal"""
    print("Generador de alias para proyectos")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "list":
            show_available_projects()
        elif command == "script":
            create_script_file()
        elif command == "aliases":
            create_aliases()
        else:
            print("ERROR: Comando no reconocido")
            print("Comandos disponibles: list, script, aliases")
    else:
        print("Opciones:")
        print("  python create_project_aliases.py list    - Mostrar proyectos disponibles")
        print("  python create_project_aliases.py script  - Crear script de navegación")
        print("  python create_project_aliases.py aliases - Crear alias en .bash_aliases")
        
        # Por defecto, mostrar proyectos
        show_available_projects()

if __name__ == "__main__":
    main()
