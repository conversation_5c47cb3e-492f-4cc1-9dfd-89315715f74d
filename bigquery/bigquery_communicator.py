from google.cloud import bigquery


def execute_query(query, project='analysis-seeker'):
    client = bigquery.Client(project=project)

    result = client.query(query)

    # Get the results and print them
    return [dict(row) for row in result]

def insert_rows(full_table_name, rows_to_insert, project):
    if rows_to_insert:
        client = bigquery.Client(project=project)
        errors = client.insert_rows_json(full_table_name, rows_to_insert, ignore_unknown_values=True)
        return errors
    else:
        return []

if __name__ == '__main__':
    query = '''
        SELECT
  count(*) as NUM,
  hotel_code
FROM
  `analysis-seeker.bi_dataset.USER_ENTRY_P`
WHERE
  DATE(timestamp) > "2023-04-07"
GROUP BY
  hotel_code
order by NUM desc
    '''
    print(execute_query(query))
