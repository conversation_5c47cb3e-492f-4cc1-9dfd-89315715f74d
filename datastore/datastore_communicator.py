from threading import RLock

try:
	from cachetools import TTLCache
	from google.auth import compute_engine
	from google.cloud import datastore
	from google.cloud.datastore import Entity
except:
	print("This project won't be able to access datastore")

from paraty_commons_3.decorators.timerdecorator import timeit
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.logging.my_gae_logging import logging

from paraty_commons_3.model import excluded_from_index


class Config(object):

	MANAGER_URL = 'https://admin-hotel.appspot.com'
	PROJECT = 'paraty_commons_3' #To be changed
	NAMESPACE = ''
	DEV = True
	TESTING = False


def build_key(entity_name, id, hotel_code=None):
	my_client = _get_datastore_client(hotel_code)

	if id:
		new_key = my_client.key(entity_name, id)
	else:
		new_key = my_client.key(entity_name)

	return new_key


def save_to_datastore(entity_name, id, properties, hotel_code=None, exclude_from_indexes=None):

	new_key = build_key(entity_name, id, hotel_code)

	if exclude_from_indexes is None:
		exclude_from_indexes = excluded_from_index.get_excluded_from_index(entity_name)

	new_entity = Entity(key=new_key, exclude_from_indexes=exclude_from_indexes)

	new_entity.update(properties)

	my_client = _get_datastore_client(hotel_code)
	my_client.put(new_entity)

	return new_entity.key.id


def save_multiple_entities(entity_name, ids, entities_properties, hotel_code=None):

	#Nothing to do in an empty list
	if not ids:
		return

	exclude_from_indexes = excluded_from_index.get_excluded_from_index(entity_name)

	entities = []

	for i, currenty_properties in enumerate(entities_properties):
		new_key = build_key(entity_name, ids[i], hotel_code)
		new_entity = Entity(key=new_key, exclude_from_indexes=exclude_from_indexes)
		new_entity.update(currenty_properties)

		entities.append(new_entity)

	my_client = _get_datastore_client(hotel_code)

	my_chunks = chunks(entities, 500)
	for chunk in my_chunks:
		my_client.put_multi(chunk)

	# my_client.put_multi(entities)

	return [x.key.id for x in entities]


def save_entity(new_entity, hotel_code=None, force_excluded_from_index=None):

	my_client = _get_datastore_client(hotel_code=hotel_code)

	#Exclusions are not saved in the entity when the fields are not populated
	if not new_entity.exclude_from_indexes:
		new_entity.exclude_from_indexes = excluded_from_index.get_excluded_from_index(new_entity.kind)

	if force_excluded_from_index:
		new_entity.exclude_from_indexes = force_excluded_from_index

	return my_client.put(new_entity)


def chunks(lst, n):
	"""Yield successive n-sized chunks from lst."""
	for i in range(0, len(lst), n):
		yield lst[i:i + n]


def save_entity_multi(multiple_entities, hotel_code=None):
	'''
	After using this, it is recommended that you verify that everything has been saved as expected
	'''

	my_client = _get_datastore_client(hotel_code=hotel_code)

	#fmatheis, Let's save 10 by 10 to prevent entities from dissapearing as happened with Ona corporate
	my_chunks = chunks(multiple_entities, 100)
	for chunk in my_chunks:
		my_client.put_multi(chunk)


calls = set()
def _check_duplicates(hotel_code, entity_name, search_params):
	key = '%s_%s_%s' % (hotel_code, entity_name, search_params)
	if key in calls:
		logging.info("duplicated: %s", key)
	else:
		calls.add(key)

@timeit
def get_using_entity_and_params(entity_name, search_params=[], keys_only=False, projections=None, hotel_code=None, return_cursor=False, order_by=None, limit=None):
	'''
	i.e.
	get_using_entity_and_params('UserModel', [('timestamp','>', '2019')], keys_only=False, projections=None)

	Returns an iterator
	'''

	try:
		from paraty import Config
	except Exception as e:
		logging.warning(e)

	if Config.DEV:
		logging.info("[DATASTORE] <---------- get_using_entity_and_params: %s, %s, %s", hotel_code, entity_name, search_params)

	# fmatheis, comment me, just for testing purposes
	# _check_duplicates(hotel_code, entity_name, search_params)

	client = _get_datastore_client(hotel_code)

	query = client.query(kind=entity_name)

	if projections:
		query.projection = projections

	for current_param in search_params:
		query.add_filter(current_param[0], current_param[1], current_param[2])

	if keys_only:
		query.keys_only()

	if order_by:
		query.order = [order_by]

	if return_cursor:
		return query.fetch(limit=limit)
	else:
		return list(query.fetch(limit=limit))


def get_available_namespaces(hotel_code=None):

	client = _get_datastore_client(hotel_code)

	query = client.query(kind="__namespace__")
	all_namespaces = list(query.fetch())
	return all_namespaces


def delete_entity(class_name, entity_id, hotel_code=None):

	client = _get_datastore_client(hotel_code=hotel_code)

	my_key = client.key(class_name, entity_id)

	client.delete(my_key)


def delete_entity_multi(list_of_entity_keys, hotel_code=None):
	client = _get_datastore_client(hotel_code=hotel_code)

	my_chunks = chunks(list_of_entity_keys, 100)
	for chunk in my_chunks:
		client.delete_multi(chunk)


def get_entity_by_key(my_key, hotel_code):
	try:
		from paraty import Config
	except Exception as e:
		logging.warning(e)

	if Config.DEV:
		logging.info("[DATASTORE] <---------- get_entity_by_key: %s, %s", hotel_code, my_key)

	client = _get_datastore_client(hotel_code=hotel_code)

	# The kind for the new entity
	entity = client.get(my_key)

	return entity


def get_entity(class_name, entity_id, hotel_code=None):
	try:
		from paraty import Config
	except Exception as e:
		logging.warning(e)

	if Config.DEV:
		logging.info("get_entity: %s, %s, %s", hotel_code, class_name, entity_id)

	client = _get_datastore_client(hotel_code=hotel_code)

	my_key = client.key(class_name, entity_id)

	# The kind for the new entity
	entity = client.get(my_key)

	return entity


#Based on this, the tokens last up to 1 hour: https://cloud.google.com/iam/docs/creating-short-lived-service-account-credentials
#NOTE THAT THIS IS NOT THREADSAFE!!!
cache = TTLCache(maxsize=100, ttl=1800)
lock = RLock()

def _get_datastore_client(hotel_code=None, force_renew_client=False):
	try:
		from paraty import Config
	except Exception as e:
		logging.warning(e)

	#Current project by default
	if not hotel_code:
		project = Config.PROJECT
		namespace = Config.NAMESPACE

	#Hotel Manager entities
	elif hotel_code == 'admin-hotel':
		project = 'admin-hotel'
		namespace = ''

	elif not is_hotel_datastore(hotel_code):
		project, namespace = hotel_code.split(":")

	else:
		project, namespace = hotel_manager_utils.get_hotel_project_and_namespace(hotel_code)

	cache_key = '%s_%s' % (project, namespace)

	with lock:
		if not force_renew_client and cache_key in cache:
			try:
				return cache[cache_key]
			except Exception as e:
				#Note that TTL might raise Key Error, even if the entry is there
				logging.warning(e)


	if Config.DEV:
		new_client = datastore.Client(project=project, namespace=namespace)
	else:

		if hasattr(Config, 'CREDENTIALS') and Config.CREDENTIALS:
			credentials = Config.CREDENTIALS
		else:
			credentials = compute_engine.Credentials()

		new_client = datastore.Client(project=project, namespace=namespace, credentials=credentials)

	with lock:
		cache[cache_key] = new_client

	return new_client


def is_hotel_datastore(hotel_code):
	return hotel_code != 'admin-hotel' and not ':' in hotel_code


def map_dictionaries_to_datastore_entities(dictionaries):
	entities = []
	EXCLUDED_PROPERTIES = ["id", "project", "namespace", "kind"]
	if len(dictionaries) > 0:
		first_dictionary = dictionaries[0]
		client = datastore.Client(project=first_dictionary["project"], namespace=first_dictionary["namespace"])
		for dictionary in dictionaries:
			key = client.key(dictionary["kind"], dictionary["id"], namespace=dictionary["namespace"])
			entity = datastore.Entity(key=key)

			for key, value in dictionary.items():
				if key not in EXCLUDED_PROPERTIES:
					entity[key] = value
			entities.append(entity)
	return entities
