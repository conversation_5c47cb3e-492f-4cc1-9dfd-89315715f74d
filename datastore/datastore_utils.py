import array
import base64
import time

import requests
from google.cloud.datastore import Key

from paraty_commons_3.datastore import datastore_communicator
from paraty_commons_3.datastore.datastore_communicator import save_to_datastore
from paraty_commons_3.datastore.legacy_protocol_buffer import Decoder
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.hotel_manager import hotel_manager_utils
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_metadata_by_application_id, get_hotel_by_application_id, get_hotel_metadata_by_application_id_without_cache
from paraty_commons_3.logging.my_gae_logging import logging

import urllib3
from urllib3.exceptions import InsecureRequestWarning
# Suppress only the InsecureRequestWarning
urllib3.disable_warnings(InsecureRequestWarning)

DEFAULT_AUTHORIZATION = "Basic cGFibDA6bWF0aGUxcw=="


def id_to_entity_key(hotel_code, entity_key):
    # Patch to fix problem after upgrading libraries (avoid AttributeError: 'Key' object has no attribute '_database')
    if not hasattr(entity_key, "_database"):
        entity_key._database = None

    return entity_key.to_legacy_urlsafe(location_prefix=get_location_prefix(hotel_code)).decode('utf-8')


def get_project_and_namespace(hotel_code):
    '''
    i.e. hotel-zen
    Returns 'hotel-zen',''

    i.e. holiday-polynesia
    Returns 'holiday-world','holiday-polynesia'
    '''
    hotel = hotel_manager_utils.get_hotel_by_application_id(hotel_code)
    return get_project_and_namespace_from_hotel(hotel)


def get_inner_url(hotel):
    '''
    i.e. hotel-zen
    Returns 'hotel-zen',''

    i.e. holiday-polynesia
    Returns 'holiday-world','holiday-polynesia'
    '''

    hotel_url = hotel['url']
    if not 'multi' in hotel_url:
        inner_url = "https://" + hotel['applicationId'] + ".appspot.com"
    else:

        host = hotel_url.replace("rest-", hotel['applicationId'] + "-").replace("/multi/" + hotel['applicationId'], "")
        namespace = hotel['applicationId']
        # application_id = host.replace("https://", "").replace(".appspot.com", "").replace("-dot-", "").replace(hotel['applicationId'], "")
        application_id = host.replace("https://", "").split("-dot-")[-1].replace(".appspot.com", "")

        inner_url = 'https://%s-dot-%s.appspot.com' % (namespace, application_id)

    return inner_url


def get_project_and_namespace_from_hotel(hotel):
    '''
    i.e. hotel-zen
    Returns 'hotel-zen',''

    i.e. holiday-polynesia
    Returns 'holiday-world','holiday-polynesia'
    '''

    hotel_url = hotel['url']
    if not 'multi' in hotel_url:
        host = "https://" + hotel['applicationId'] + ".appspot.com"
        namespace = ''
        application_id = hotel['applicationId']

    else:
        host = hotel_url.replace("rest-", hotel['applicationId'] + "-").replace("/multi/" + hotel['applicationId'], "")
        namespace = hotel['applicationId']
        application_id = host.replace("https://", "").replace(".appspot.com", "").replace("-dot-", "").replace(hotel['applicationId'], "", 1)

    return application_id, namespace


def alphanumeric_to_id(alphanumericKey):
    '''
    Returns the Integer ID from a given alphanumeric GAE Key
    i.e 'ag1zfmFkbWluLWhvdGVschcLEhBIb3RlbEFwcGxpY2F0aW9uGKEfDA' -> 4001
    '''

    if not alphanumericKey:
        return 0

    k = Key.from_legacy_urlsafe(alphanumericKey)
    return k.id


def alphanumeric_to_key(alphanumeric_key):
    if not alphanumeric_key:
        return 0

    k = Key.from_legacy_urlsafe(alphanumeric_key)
    return k


def entity_id_to_alphanumeric_key(entity_id: int, kind, hotel_code):
    '''
    Returns the Alphanumeric GAE Key from a given Integer ID
    i.e 4001, 'HotelApplication', 'hotel-zen' -> 'ag1zfmFkbW
    '''
    if hotel_code == 'admin-hotel':
        application_id, namespace = 'admin-hotel', None
        location_prefix = 's~'
    else:
        application_id, namespace = get_project_and_namespace(hotel_code)
        location_prefix = get_location_prefix(hotel_code)

    if not namespace:  # If we don't have a namespace, need None, not an empty string
        namespace = None

    return Key(kind, entity_id, project=location_prefix + application_id, namespace=namespace).to_legacy_urlsafe().decode('utf-8')


def _get_local_project_name():
    actual_project = ''
    try:
        from paraty.config import Config
        actual_project = Config.PROJECT
    except Exception:
        pass
    return actual_project


# Simple caching
location_prefix_cache = {}

def get_location_prefix(hotel_code):

    if hotel_code in location_prefix_cache:
        return location_prefix_cache[hotel_code]

    try:
        result = requests.get(f"https://europe-west1-build-tools-2.cloudfunctions.net/get_hotel_location_prefix?hotel_code={hotel_code}&referrer={_get_local_project_name()}", timeout=20, verify=False)
        if result.status_code == 200:
            location_prefix_cache[hotel_code] = result.content.decode('utf-8')
            return location_prefix_cache[hotel_code]
    except Exception as e:
        time.sleep(2)
        result = requests.get(f"https://europe-west1-build-tools-2.cloudfunctions.net/get_hotel_location_prefix?hotel_code={hotel_code}&referrer={_get_local_project_name()}", timeout=20, verify=False)
        if result.status_code == 200:
            location_prefix_cache[hotel_code] = result.content.decode('utf-8')
            return location_prefix_cache[hotel_code]


    logging.warning("It was not possible to obtain the location prefix from cloudfunctions, trying to obtain it from datastore")

    # Failover to the old way using HotelMetadata

    # Get HotelMetada
    hotel_metadata = None
    try:
        hotel_metadata = get_hotel_metadata_by_application_id(hotel_code)
    except Exception as e:
        logging.warning("cloudfunctions doesn't return json not valid!")

    if 'location_prefix' in hotel_metadata and hotel_metadata['location_prefix'] is not None:
        return hotel_metadata['location_prefix']

    logging.warning("Location prefix not found for hotel_code: %s" % hotel_code)
    raise Exception("Location prefix not found for hotel_code: %s" % hotel_code)


def __decode_alphanumeric_key(alphanumericKey):
    modulo = len(alphanumericKey) % 4
    if modulo != 0:
        alphanumericKey += ('=' * (4 - modulo))

    _str = str(alphanumericKey)
    encoded_pb = base64.urlsafe_b64decode(_str)
    a = array.array('B')
    a.frombytes(encoded_pb)
    d = Decoder(a, 0, len(a))
    data = __decode_data(d)
    return data


def __decode_data(d):
    output = {}
    while d.avail() > 0:
        tt = d.getVarInt32()
        if tt == 114:
            length = d.getVarInt32()
            tmp = Decoder(d.buffer(), d.pos(), d.pos() + length)
            d.skip(length)

            while tmp.avail() > 0:
                tt2 = tmp.getVarInt32()
                if tt2 == 11:
                    while 1:
                        tt3 = tmp.getVarInt32()
                        if tt3 == 12: break
                        if tt3 == 18:
                            output['type'] = tmp.getPrefixedString()
                            continue
                        if tt3 == 24:
                            output['id'] = tmp.getVarInt64()
                            continue
                        if tt3 == 34:
                            output['name'] = tmp.getPrefixedString()
                            continue

                        tmp.skipData(tt3)

            continue

        if tt == 106:
            output['app'] = d.getPrefixedString()
            continue

        if tt == 162:
            output['namespace'] = d.getPrefixedString()
            continue

        if tt == 186:
            output['database'] = d.getPrefixedString()
            continue

        d.skipData(tt)

    return output


if __name__ == '__main__':
    result = get_location_prefix('hotel-puentereal')
    print(result)

    result = get_location_prefix('hotel-puentereal')
    print(result)
